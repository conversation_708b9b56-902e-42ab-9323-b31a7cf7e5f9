import { h, render } from 'vue'
import { getAllDateRange } from '@/utils/all-date-range.js'

/**
 * 将给定的组件和属性转换为HTML字符串
 *
 * @param {any|Object} component - 组件
 * @param {Object} props - 传递给组件的属性对象
 * @returns {String} - 组件渲染后的HTML字符串
 */
export const NodeToHtml = (component, props, slots) => {
  const dom = document.createElement('div')
  const vnode = h(component, props, slots)
  render(vnode, dom)
  return dom.innerHTML
}

/**
 * 根据名称和年份获取对应的最大月份或默认值 12
 * @param {string} name - 数据源名称
 * @param {string|number} year - 年份
 * @returns {string} - 返回最大月份或 '12'
 */
export async function getMonthByNameAndYear(name, year) {
  // console.log('name', name, 'year', year)
  const { maxYear, maxMonth } = await getAllDateRange(name)
  if (maxYear && maxYear === String(year)) {
    return maxMonth
  }
  return '12'
}

/**
 * 为数据中的玉柴项设置颜色，支持一维和二维数组
 * @param {Array} data - 要处理的数据数组
 * @param {number} [index=0] - 颜色索引，默认为0
 * @returns {Array} 处理后的数据
 */
export function setYuchaiColor(data, index = 0) {
  if (!Array.isArray(data)) return data
  const yucaiColorList = ['#c00000', '#FE4B4B', '#EE8A20']
  // 判断一维数组还是二维数组
  if (Array.isArray(data[0]) && data[0].length > 0) {
    // 处理二维数组
    data.forEach((item, colorIndex) => {
      if (item && typeof item === 'object') {
        // 处理item.data数组
        if (Array.isArray(item.data)) {
          item.data.forEach(bar => {
            if (bar.name && bar.name.includes('玉柴')) {
              bar.itemStyle = { color: yucaiColorList[colorIndex] }
            }
          })
        }
        // 处理item本身是数据项的情况
        else if (item.name && item.name.includes('玉柴')) {
          item.itemStyle = { color: yucaiColorList[colorIndex] }
        }
      }
    })
  } else {
    data.forEach((item, colorIndex) => {
      if (item && typeof item === 'object') {
        // 处理item.data数组
        if (Array.isArray(item.data)) {
          item.data.forEach(bar => {
            if (bar.name && bar.name.includes('玉柴')) {
              bar.itemStyle = { color: yucaiColorList[colorIndex] }
            }
          })
        }
      }
    })
  }

  return data
}
/**
 * TooltipFormatter 函数用于生成 tooltips（工具提示）的 HTML 内容
 * 它接受一个 TooltipComponent 组件，以及额外的参数和数据，来渲染 tooltips
 *
 * @param {Object} TooltipComponent - 用于渲染 tooltip 的组件
 * @param {Object} params - 传递给 TooltipComponent 的参数，通常包含数据点信息
 * @param {Object} data - 额外的数据对象，将被合并到传递给 TooltipComponent 的 props 中
 * @returns {string} - 返回 TooltipComponent 渲染后的 HTML 字符串
 */
export const TooltipFormatter = (TooltipComponent, params, data) => {
  return NodeToHtml(TooltipComponent, {
    params,
    ...data
  })
}

export function removeDataAnchor(htmlString) {
  try {
    // 创建临时DOM元素来解析HTML字符串
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlString

    // 查找包含data-anchor的元素
    const element = tempDiv.querySelector('[data-anchor]')
    if (element) {
      // 移除data-anchor属性
      element.removeAttribute('data-anchor')
    }

    // 返回处理后的HTML字符串
    return tempDiv.innerHTML
  } catch (error) {
    console.error('Failed to remove data-anchor:', error)
    return htmlString // 出错时返回原始字符串
  }
}

/**
 * 数组进行处理，排序 其他 到第一位
 * @param {*} arr
 * @returns
 */
export function seriesSortOtherFirst(arr) {
  if (!Array.isArray(arr)) return arr
  const otherIndex = arr.findIndex(
    x => x.name && (x.name.includes('其他') || x.name.includes('其它'))
  )
  if (otherIndex > -1) {
    const [otherItem] = arr.splice(otherIndex, 1)
    arr.push(otherItem)
  }
  return arr
}

/**
 * 对图例数据进行排序，将包含"其他"或"其它"的项目放到数组第一位
 * @param {Array} legendData - 图例数据数组，可以是字符串数组或对象数组
 * @param {string} nameKey - 当数组元素为对象时，指定名称字段的key，默认为'name'
 * @returns {Array} 排序后的图例数组
 *
 * @example
 * // 字符串数组示例
 * const legends1 = ['玉柴', '潍柴', '其他', '康明斯']
 * const sorted1 = sortLegendWithOtherFirst(legends1)
 * // 结果: ['其他', '玉柴', '潍柴', '康明斯']
 *
 * // 对象数组示例
 * const legends2 = [
 *   { name: '玉柴', value: 100 },
 *   { name: '其它', value: 50 },
 *   { name: '潍柴', value: 80 }
 * ]
 * const sorted2 = sortLegendWithOtherFirst(legends2)
 * // 结果: [{ name: '其它', value: 50 }, { name: '玉柴', value: 100 }, { name: '潍柴', value: 80 }]
 */
export function sortLegendWithOtherFirst(legendData, nameKey = 'name') {
  if (!Array.isArray(legendData)) return legendData

  const result = [...legendData]
  const otherItems = []

  // 查找包含"其他"或"其它"的项目
  if (typeof result[0] === 'string') {
    // 字符串数组
    for (let i = result.length - 1; i >= 0; i--) {
      if (result[i] && (result[i].includes('其他') || result[i].includes('其它'))) {
        otherItems.unshift(result.splice(i, 1)[0])
      }
    }
  } else if (typeof result[0] === 'object' && result[0] !== null) {
    // 对象数组
    for (let i = result.length - 1; i >= 0; i--) {
      if (result[i] && result[i][nameKey] && (result[i][nameKey].includes('其他') || result[i][nameKey].includes('其它'))) {
        otherItems.unshift(result.splice(i, 1)[0])
      }
    }
  }

  // 将所有"其他"项添加到数组开头
  result.unshift(...otherItems)

  return result
}



/**
 * 对图表图例进行排序
 * @param {Array} series - 图表数据系列数组，每个元素应包含name属性
 * @param {Boolean} hasTruckTypes - 是否启用卡车类型排序逻辑的标识
 * @param {Array} truckTypes - 卡车类型数组，用于确定排序优先级
 * @returns {Array} 排序后的图例名称数组 
 */
export function sortChartLegend(series, hasTruckTypes, truckTypes) {
  if (hasTruckTypes) {
    const filteredSeries = series.filter(el => !el.name.includes(['3.5吨以下']))
    const sortedLegend = filteredSeries
      .map(el => el.name)
      .sort((a, b) => {
        const indexA = truckTypes.indexOf(a)
        const indexB = truckTypes.indexOf(b)

        // 如果两个都在truckTypes中，按truckTypes顺序排序
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB
        }

        // 如果只有一个是truckType，truckType优先
        if (indexA !== -1 && indexB === -1) {
          return -1
        }
        if (indexA === -1 && indexB !== -1) {
          return 1
        }

        // 都不是truckType，保持原有顺序
        return 0
      })

    return sortedLegend
  }
}

/**
 * 处理 dataType 参数，兼容单选和多选模式
 * 这个函数可以直接替换原来的 params.dataType.join() 调用
 * @param {string|array} dataType - 数据类型值（可能是字符串或数组）
 * @returns {string} - 处理后的字符串
 */
export const processDataType = (dataType) => {
  if (!dataType) {
    return ''
  }

  // 如果是数组，使用 join 方法
  if (Array.isArray(dataType)) {
    return dataType.length > 0 ? dataType.join(',') : ''
  }

  // 如果是单个值，直接转换为字符串
  if (typeof dataType === 'string' || typeof dataType === 'number') {
    return dataType.toString()
  }

  return ''
}

/**
 * 处理富文本，移除所有写死的宽度（width）内联样式和属性
 * @param {string} html - 原始的富文本HTML字符串
 * @returns {string} - 处理后的HTML字符串
 */
export function removeFixedWidth(html) {
  if (!html || typeof html !== 'string') {
    return html;
  }

  // 1. 移除 style 属性中的 width 声明
  // 匹配 style="..." 或 style='...' 中的 width: ...; 或 width: ... 或 width: ... !important;
  html = html.replace(/style\s*=\s*(['"])([^'"]*?)(['"])/gi, (match, quote, styleContent, endQuote) => {
    // 从 style 内容中移除 width 相关的声明
    let cleanedStyle = styleContent
      // 移除 width: value; (分号结尾)
      .replace(/width\s*:\s*[^;]+;\s*/gi, '')
      // 移除 width: value (末尾无分号)
      .replace(/;\s*width\s*:\s*[^;]+$/gi, '')
      // 移除单独的 width: value (整个style只有width)
      .replace(/^\s*width\s*:\s*[^;]+;\s*$/gi, '')
      // 清理可能残留的前导或尾随分号和空格
      .replace(/^;\s*/, '').replace(/\s*;$/, '')
      // 清理多个连续分号
      .replace(/;{2,}/g, ';')
      // 如果清理后style为空，则整个style属性应该被移除
      .trim();

    // 如果清理后style为空，则返回空字符串（表示移除整个style属性）
    return cleanedStyle ? `style=${quote}${cleanedStyle}${endQuote}` : '';
  });

  // 2. 移除 width 属性 (如 width="500" 或 width='500')
  html = html.replace(/\s+width\s*=\s*(['"])[^'"]*\1/gi, '');

  // 3. 如果移除 width 属性后导致 style 属性也为空，则移除空的 style 属性
  html = html.replace(/\s+style\s*=\s*(['"])['"]\s*/gi, ' ');

  // 4. 清理多余的空格
  html = html.replace(/\s+/g, ' ').trim();

  return html;
}