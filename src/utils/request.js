import axios from 'axios'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { ElNotification, ElMessageBox, ElMessage, ElLoading, genFileId } from 'element-plus'
import store from '@/store'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { tansParams, blobValidate } from '@/utils/ruoyi'
import cache from '@/plugins/cache'
import { saveAs } from 'file-saver'
import { isArray } from 'lodash'
import dayjs from "dayjs";

NProgress.configure({ showSpinner: false })

let downloadLoadingInstance
// 是否显示重新登录
let isReloginShow

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 100000
})

// request拦截器
service.interceptors.request.use(
  config => {
    NProgress.start()
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params)
      url = url.slice(0, -1)
      config.params = {}
      config.params.commandKey = store.state.app.commandKey
      config.url = url
    }
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      if (typeof config.data === 'object') {
        config.data.commandKey = store.state.app.commandKey
      }
      // TODO 受之全局处理影响，故做全局拦截判断条件修改值，如遇multipleWeight值不知何处看，定位到此便知
      if(typeof config.data === 'object' && config.data.weightMidLight &&  isArray(config.data.weightMidLight) ){
        config.data.multipleWeight = config.data.weightMidLight;
        config.data.weightMidLight = '';
      }
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime()
      }
      const sessionObj = cache.session.getJSON('sessionObj')

      if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
        cache.session.setJSON('sessionObj', requestObj)
      } else {
        const s_url = sessionObj.url // 请求地址
        const s_data = sessionObj.data // 请求数据
        const s_time = sessionObj.time // 请求时间
        const interval = 0 // 间隔时间(ms)，小于此时间视为重复提交

        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = '数据正在处理，请勿重复提交'
          console.warn(`[${s_url}]: ` + message)
          return Promise.reject(new Error(message))
        } else {
          cache.session.setJSON('sessionObj', requestObj)
        }
      }
    }
    return config
  },
  error => {
    console.log(error)
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  res => {
    NProgress.done()
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default']
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'text' || res.request.responseType === 'arraybuffer') {
      return res.data
    }
    if (code === 401) {
      if (!isReloginShow) {
        isReloginShow = true
        ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            isReloginShow = false
            store.dispatch('LogOut').then(() => {
              // 如果是登录页面不需要重新加载
              if (window.location.hash.indexOf('#/login') != 0) {
                location.href = '/index'
              }
            })
          })
          .catch(() => {
            isReloginShow = false
          })
      }
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === 500) {
      ElMessage({
        message: msg,
        type: 'error'
      })
      return Promise.reject(new Error(msg))
    } else if (code === 413) {
      ElMessage({
        message: '文件超出服务端限制',
        type: 'error'
      })
      return Promise.reject(new Error(msg))
    } else if (code !== 200) {
      ElNotification.error({
        title: msg
      })
      return Promise.reject('error')
    } else {
      return Promise.resolve(res.data)
    }
  },
  error => {
    console.log('err' + error)
    let { message } = error
    if (message == 'Network Error') {
      message = '后端接口连接异常'
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常'
    }
    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

// 通用下载方法
export function download(url, params, filename) {
  downloadLoadingInstance = ElLoading.service({
    text: '正在下载数据，请稍候',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  return service
    .post(url, params, {
      transformRequest: [
        params => {
          return tansParams(params)
        }
      ],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    })
    .then(async data => {
      const isLogin = await blobValidate(data)
      if (isLogin) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        ElMessage.error(errMsg)
      }
      downloadLoadingInstance.close()
    })
    .catch(r => {
      console.error(r)
      ElMessage.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close()
    })
}
// 通用下载方法
export function downloadJSON(url, params, filename) {
  downloadLoadingInstance = ElLoading.service({
    text: '正在下载数据，请稍候',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  return service
    .post(url, params, {
      headers: { 'Content-Type': 'application/json' },
      responseType: 'blob'
    })
    .then(async data => {
      const isLogin = await blobValidate(data)
      if (isLogin) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        ElMessage.error(errMsg)
      }
      downloadLoadingInstance.close()
    })
    .catch(r => {
      console.error(r)
      ElMessage.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close()
    })
}

export function uploadOBS(path, file, onUploadProgress) {
  console.log(file)
  const contentType = file.type
  const filePath = `${path}/${dayjs(new Date()).format('YYYYMMDD')}/${genFileId()}/${file.name}`
  return service({
    url: `upload/obs/temporarySignature`,
    method: 'get',
    params: {
      filePath: filePath,
      contentType: contentType,
      method: 'put'
    }
  }).then(res => {
    return new Promise((resolve, reject) => {
      console.log('temporarySignature', res)
      if (!res.signedUrl) {
        reject("获取OBS上传URL失败！")
        return
      }
      let time = Date.now()
      let loaded = 0
      const data = {
        method : 'PUT',
        url : res.signedUrl,
        withCredentials: false,
        headers : res.actualSignedRequestHeaders || {},
        maxRedirects : 0,
        responseType : 'text',
        data : file,
        onUploadProgress(e) {
          const complete = e.loaded / e.total * 100
          if (complete && onUploadProgress) {
            const duration = (Date.now() - time) / 1000
            const speed = (e.loaded - loaded) / duration
            console.log('complete => ' + complete + '%', 'speed => ' + speed)
            onUploadProgress(complete.toFixed(2) - 0, renderSize(speed) + '/s', speed)
          }
          loaded = e.loaded;
          time = Date.now()
        }
      };
      service.request(data).then(() => {
        console.log(filePath)
        resolve(filePath)
      }).catch(function (err) {
        console.log('Creating object using temporary signature failed!');
        console.log(err);
        console.log('\n');
        reject("OBS上传失败！")
      });
    })
  })
}

function renderSize(filesize) {
  if (filesize == null || filesize === '') {
    return "0 B";
  }
  const unitArr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  let index = 0;
  const srcSize = parseFloat(filesize);
  index = Math.floor(Math.log(srcSize) / Math.log(1024));
  let size = srcSize / Math.pow(1024, index);
  size = size.toFixed(2); // 保留两位小数
  return size + " " + unitArr[index];
}

export default service
