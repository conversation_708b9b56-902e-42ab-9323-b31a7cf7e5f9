<template>
  <div>
    <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
      <el-row :gutter="16">
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="year">
            <el-date-picker
              v-model="params.year"
              type="year"
              value-format="YYYY"
              format="YYYY"
              :disabled-date="disabledFeatureDate"
              placeholder="年份"
              :clearable="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="8" :sm="8" :md="3">
          <el-form-item prop="pointerType">
            <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
              <el-option
                v-for="item in dictsPointerType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="8" :sm="8" :md="3">
          <el-form-item v-if="params.pointerType === '2'" prop="month">
            <el-select v-model="params.month" placeholder="月累" style="width: 100%">
              <el-option
                v-for="item in newDictsMonthTotal"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
            <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
              <el-option
                v-for="item in newDictsQuarter"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else prop="month">
            <el-select v-model="params.month" placeholder="月度" style="width: 100%">
              <el-option
                v-for="item in newDictsMonth"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <DictsResource
          :form="params"
          :dicts="data.linkageData"
          :props="[
            {
              name: '数据来源',
              key: 'dataSource',
              clearable: true
            },
            {
              name: '板块',
              key: 'segment',
              clearable: true,
              disabled: true
            },
            {
              name: '细分市场一',
              key: 'subMarket1',
              clearable: true
            },
            {
              name: '细分市场二',
              hide: true,
              key: 'subMarket2'
            }
          ]"
          :propsEngineFactory="{ name: '发动机厂', key: 'engineFactory', show: true }"
          :propsManuFacturer="{ name: '主机厂', key: 'manuFacturer', show: true, clearable: true }"
          :propsFuelType="{ name: '燃料', key: 'fuelType', show: true, type: 'B' }"
          :propsWeightMidLight="{
            name: '重中轻',
            key: 'weightMidLight',
            show: true,
            disabled: false
          }"
          :propsDataType="{
            name: '数据扩展',
            key: 'dataType',
            show: true
          }"
          :xs="8"
          :sm="8"
          :md="3"
        />
        <!-- type 燃料类型（A-新能源 B-汽油） -->
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="cylinders">
            <el-select
              v-model="params.cylinders"
              placeholder="气缸数"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in systemDicts.sys_cylinder_count"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col
          v-if="params.dataSource === '1'"
          :xs="8"
          :sm="8"
          :md="params.dataType.length > 2 ? 5 : 3"
        >
          <el-form-item prop="dataType">
            <el-select
              v-model="params.dataType"
              multiple
              placeholder="数据扩展"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in dictDataType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="province">
            <el-select v-model="params.province" placeholder="省" clearable style="width: 100%">
              <el-option
                v-for="item in cityData"
                :key="item.code"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item>
            <el-button type="primary" @click="toggleSearch">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-segmented
      ref="segmentedRef"
      v-model="params.manuFacturerTabs"
      :options="comDictsManuFacturer"
      @change="toggleSearch('segmented')"
      class="segmented"
    >
      <template #default="scope">
        <div>{{ scope.item.label }}</div>
      </template>
    </el-segmented>
  </div>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType } from '@/utils/common/dicts.js'
import useInnerData from '@/utils/hooks/innerData.js'
import { throttle } from '@/utils'
// 用户要的默认展示主机厂数据
// 卡车
const defaultDictsManuFacturerKa = [
  '徐工',
  '三一',
  '柳汽',
  '东风商用',
  '福田戴姆勒',
  '一汽解放（青岛）',
  '陕重汽',
  '重汽',
  '江淮重卡',
  '吉利',
  '东风股份'
]
// 客车
const defaultDictsManuFacturerKe = [
  '宇通',
  '厦门金龙',
  '苏州金龙',
  '厦门金旅',
  '吉利',
  '福田欧辉',
  '中通',
  '安凯'
]

const store = useStore()

const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      month: '', // 月
      quarter: '', // 季度
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      dataSource: dataSource, // 数据来源（货运新增）
      subMarket1: '', // 细分市场1
      manuFacturerTabs: '',
      manuFacturer: '', // 主机厂
      engineFactory: '', // 发动机厂
      fuelType: '', // 燃料
      weightMidLight: '', // 车型
      cylinders: '', // 气缸数
      province: '', // 省
      dataType: [],
      segment: '' // 板块
    })
  }
})
const systemDicts = computed(() => store.state.dicts.dicts)
const cityData = computed(() => store.state.dicts.cityData)
// const { disabledFeatureDate } = formValidate()
const emit = defineEmits(['change'])
const data = reactive({
  linkageData: [], // 多级联动数据
  dictsManuFacturer: []
})
const params = reactive({ ...toRaw(props.params) })
let length = ref(data.dictsManuFacturer.length)

// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)
watch(
  () => params.dataSource,
  val => {
    switch (val) {
      case '1':
        // 上险数
        initDateRange('上险数')
        params.segment = '商用车'
        params.subMarket1 = '卡车'
        getDictsManuFacturer('卡车')
      case '6':
        // 货运新增数
        initDateRange('货运新增数')
        params.segment = '商用车'
        params.subMarket1 = '卡车'
        getDictsManuFacturer('卡车')
        break
    }
    if (val !== '1') {
      params.dataType = []
    }
  }
)
watch(
  () => params.subMarket1,
  val => {
    getDictsManuFacturer(val)
  },
  {
    immediate: true
  }
)
/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch(ev) {
  if (ev === 'segmented') {
    params.manuFacturer = params.manuFacturerTabs
  } else {
    params.manuFacturerTabs = params.manuFacturer
  }
  const data = JSON.parse(JSON.stringify(toRaw(params)))
  delete data.subMarket2
  emit('change', data)
}
const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['上险数', '货运新增数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}
const segmentedRef = ref(null)
// const getDictsManuFacturer = ev => {
//   // const dicts = JSON.parse(JSON.stringify(ev))
//   const newDicts = [] // 展示的主机厂
//   // 排在后面的字典
//   // const lastDicts = dicts.filter(
//   //   item => defaultDictsManuFacturer.findIndex(el => el !== item.label) !== -1
//   // )
//   defaultDictsManuFacturer.forEach(el => {
//     newDicts.push({ label: el, value: el })
//   })
//   // newDicts.push(...lastDicts)
//   data.dictsManuFacturer = newDicts
//   length = ref(data.dictsManuFacturer.length)
//   params.manuFacturer = newDicts && newDicts.length > 0 ? newDicts[0].value : ''
//   resizeDom()
// }
function getDictsManuFacturer(ev) {
  const newDicts = []
  if (ev === '卡车') {
    defaultDictsManuFacturerKa.forEach(el => {
      newDicts.push({ label: el, value: el })
    })
  } else if (ev === '客车') {
    defaultDictsManuFacturerKe.forEach(el => {
      newDicts.push({ label: el, value: el })
    })
  }
  data.dictsManuFacturer = newDicts
  length = ref(data.dictsManuFacturer.length)
  params.manuFacturerTabs = newDicts && newDicts.length > 0 ? newDicts[0].value : ''
  params.manuFacturer = newDicts && newDicts.length > 0 ? newDicts[0].value : ''
  resizeDom()
}

const comDictsManuFacturer = computed(() => {
  return data.dictsManuFacturer.slice(0, length.value)
})
function getPercent() {
  const curWidth = segmentedRef.value.$el.offsetWidth
  const parentWidth = segmentedRef.value.$parent.$el.offsetWidth
  return parentWidth / curWidth
}
function resizeDom() {
  return throttle(() => {
    if (!segmentedRef.value) return
    const percent = getPercent()
    length.value = Math.floor(comDictsManuFacturer.value.length * percent.toFixed(2))
    nextTick(() => {
      const percent = getPercent()
      if (percent < 1) {
        length.value--
      }
    })
  })()
}

onMounted(() => {
  resizeDom()
  if (window.ResizeObserver) {
    // 使用 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      resizeDom()
    })

    // 开始监听容器大小变化
    resizeObserver.observe(segmentedRef.value.$parent.$el)
  } else {
    window.addEventListener('resize', () => {
      resizeDom()
    })
  }
})

initDateRange('上险数', true)
getDictsData()
</script>

<style scoped lang="scss">
.search-form {
  margin-bottom: 0;
}
.segmented {
  margin: 0px 0;
}
</style>
