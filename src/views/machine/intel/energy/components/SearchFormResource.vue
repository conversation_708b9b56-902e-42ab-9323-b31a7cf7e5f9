<template>
  <div style="overflow: hidden">
    <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
      <el-row :gutter="16">
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="year">
            <el-date-picker
              v-model="params.year"
              type="year"
              value-format="YYYY"
              format="YYYY"
              :disabled-date="disabledFeatureDate"
              placeholder="年份"
              :clearable="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="8" :sm="8" :md="3">
          <el-form-item prop="pointerType">
            <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
              <el-option
                v-for="item in dictsPointerType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="8" :sm="8" :md="3">
          <!-- TODO: 指标类型字典字典变换需要注意修改 -->
          <el-form-item v-if="params.pointerType === '2'" prop="month">
            <el-select v-model="params.month" placeholder="月累" style="width: 100%">
              <el-option
                v-for="item in newDictsMonthTotal"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
            <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
              <el-option
                v-for="item in newDictsQuarter"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else prop="month">
            <el-select v-model="params.month" placeholder="月度" style="width: 100%">
              <el-option
                v-for="item in newDictsMonth"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <DictsResource
          :form="params"
          :dicts="data.linkageData"
          :props="[
            {
              name: '数据来源',
              key: 'dataSource',
              // hide: true,
              clearable: true
            },
            {
              name: '板块',
              key: 'segment',
              clearable: true
            },
            // {
            //   name: '细分市场一',
            //   key: 'subMarket1xx',
            //   hide: true
            // }
            {
              name: '细分市场一',
              key: 'subMarket1',
              hide: true,
              clearable: true
            },
          ]"
          :propsManuFacturer="{ name: '主机厂', key: 'placeholder', show: false }"
          :propsFuelType="{ name: '燃料', key: 'fuelType', show: true, type: 'A' }"
          :propsBreed="{ name: '品系', key: 'breed', show: false, disabled: data.disabledBreed }"
          :propsWeightMidLight="{
            name: '重中轻',
            key: 'weightMidLight',
            show: true
          }"
          :propsDataType="{
            name: '数据扩展',
            key: 'dataType',
            show: true
          }"
          
        />
        <!-- <el-col :span="4" :xs="8" :sm="8" :md="3">
          <el-form-item prop="vehicleType">
            <el-select
              v-model="params.vehicleType"
              placeholder="重中轻"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params.subMarket1 == '卡车'
                  ? systemDicts.sys_weight_mid_light
                  : systemDicts.sys_weight_mid_light_bus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="4" :xs="8" :sm="8" :md="3">
          <el-form-item>
            <el-button type="primary" @click="toggleSearch">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-segmented
        ref="segmentedRef2"
        v-model="params.subMarket1"
        :options="data.defaultsubMarket1"
        @change="setSubMarket1Mod"
        class="segmented"
      >
        <template #default="scope">
          <div>{{ scope.item.label }}</div>
        </template>
      </el-segmented>
    </div>
    <el-segmented
      ref="segmentedRef"
      v-model="params.manuFacturer"
      :options="comDictsManuFacturerList"
      @change="toggleSearch2"
      class="segmented"
    >
      <template #default="scope">
        <div>{{ scope.item.label }}</div>
      </template>
    </el-segmented>
  </div>
</template>

<script setup>
import { throttle } from '@/utils'
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType } from '@/utils/common/dicts.js'
// import formValidate from '@/utils/hooks/formValidate.js'
import useInnerData from '@/utils/hooks/innerData.js'
import { toRef, watch } from 'vue'

const store = useStore()

const emit = defineEmits(['change'])
const systemDicts = computed(() => store.state.dicts.dicts)

const segmentedRef = ref(null)
const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      month: '', // 月
      quarter: '', // 季度
      queryType: '', // 查询类型0发动机，1整车
      segment: '', // 板块
      subMarket1: '', // 细分市场1
      subMarket2: '', //  细分市场2
      manuFacturer: '', // 主机厂
      engineFactory: '', // 发动机厂111
      breed: '', // 品系
      fuelType: '', // 燃料
      province: '', // 省
      city: '', // 市
      vehicleType: '', // 车型
      cylinders: '', // 气缸数
      dataSource: ''
    })
  }
})
const data = reactive({
  disabledSubMarket2: false,
  disabledBreed: false,
  linkageData: [], // 多级联动数据
  dictsManuFacturer: [],
  defaultsubMarket1: [
    { label: '卡车', value: '卡车' },
    { label: '客车', value: '客车' }
  ]
})
const comDictsManuFacturerList = ref([
  { value: '徐工', label: '徐工' },
  { value: '三一', label: '三一' },
  { value: '柳汽', label: '柳汽' },
  { value: '东风商用', label: '东风商用' },
  { value: '福田戴姆勒', label: '福田戴姆勒' },
  { value: '解放青岛', label: '解放青岛' },
  { value: '陕重汽', label: '陕重汽' },
  { value: '重汽', label: '重汽' },
  { value: '江淮重卡', label: '江淮重卡' },
  { value: '吉利', label: '吉利' },
  { value: '东风股份', label: '东风股份' }
])

const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)

watch(
  () => params.dataSource,
  val => {
    switch (val) {
      case '1':
        // 上险数
        initDateRange('上险数')
        params.segment = '商用车'
      // params.subMarket1 = '卡车'
      case '6':
        // 货运新增数
        initDateRange('货运新增数')
        params.segment = '商用车'
        // params.subMarket1 = '卡车'
        break
    }
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)
watch(
  () => props.params.manuFacturer,
  val => {
    params.manuFacturer = val
  }
)
watch(
  () => props.params.manuFacturer,
  val => {
    params.manuFacturer = val
  }
)

// watch(
//   () => props.comDictsManuFacturerList,
//   val => {

//     if(val.length == 0 && !comDictsManuFacturer.value.map(x=>x.value).includes(params.manuFacturer)){
//       params.manuFacturer = comDictsManuFacturer.value[0].value;
//     }
//   }
// )

watch([() => params.subMarket2, () => params.breed], val => {
  if (val[0] && !val[1]) {
    data.disabledSubMarket2 = false
    data.disabledBreed = true
  } else if (!val[0] && val[1]) {
    data.disabledSubMarket2 = true
    data.disabledBreed = false
  } else {
    data.disabledSubMarket2 = false
    data.disabledBreed = false
  }
})

watch(
  () => params.subMarket1,
  val => {
    params.vehicleType = ''
  },
  { immediate: true }
)
function setSubMarket1Mod(val) {
  if (val == '卡车') {
    const list = [
      { value: '徐工', label: '徐工' },
      { value: '三一', label: '三一' },
      { value: '柳汽', label: '柳汽' },
      { value: '东风商用', label: '东风商用' },
      { value: '福田戴姆勒', label: '福田戴姆勒' },
      { value: '解放青岛', label: '解放青岛' },
      { value: '陕重汽', label: '陕重汽' },
      { value: '重汽', label: '重汽' },
      { value: '江淮重卡', label: '江淮重卡' },
      { value: '吉利', label: '吉利' },
      { value: '东风股份', label: '东风股份' }
    ]
    comDictsManuFacturerList.value = list
    params.manuFacturer = list[0].value
  }
  if (val == '客车') {
    // data.params.manuFacturer = "宇通"
    const list = [
      { value: '宇通', label: '宇通' },
      { value: '厦门金龙', label: '厦门金龙' },
      { value: '苏州金龙', label: '苏州金龙' },
      { value: '厦门金旅', label: '厦门金旅' },
      { value: '吉利', label: '吉利' },
      { value: '福田欧辉', label: '福田欧辉' },
      { value: '中通', label: '中通' },
      { value: '安凯', label: '安凯' }
    ]
    comDictsManuFacturerList.value = list
    params.manuFacturer = list[0].value
  }

  toggleSearch()
}

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  const data = toRaw(params)
  emit('change', data)
}

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch2(val) {
  console.log(val, 'val')
  const data = toRaw(params)

  emit('change', data)
}

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      // keyArray: ['dataSource', 'segment', 'subMarket1'],
      dataSource: ['上险数', '货运新增数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}

let length = ref(data.dictsManuFacturer.length)
const getDictsManuFacturer = ev => {
  const dicts = JSON.parse(JSON.stringify(ev))
  data.dictsManuFacturer = dicts
  length = ref(data.dictsManuFacturer.length)
  params.manuFacturer = dicts && dicts.length > 0 ? dicts[0].value : ''
  resizeDom()
}

const comDictsManuFacturer = computed(() => {
  return data.dictsManuFacturer.slice(0, length.value)
})
function getPercent() {
  const curWidth = segmentedRef.value.$el.offsetWidth
  const parentWidth = segmentedRef.value.$parent.$el.offsetWidth
  return parentWidth / curWidth
}
const resizeDom = throttle(() => {
  if (!segmentedRef.value) return
  const percent = getPercent()
  length.value = Math.floor(comDictsManuFacturer.value.length * percent.toFixed(2))
  nextTick(() => {
    const percent = getPercent()
    if (percent < 1) {
      length.value--
    }
  })
})

onMounted(() => {
  resizeDom()
  if (window.ResizeObserver) {
    // 使用 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      resizeDom()
    })

    // 开始监听容器大小变化
    resizeObserver.observe(segmentedRef.value.$parent.$el)
  } else {
    window.addEventListener('resize', () => {
      resizeDom()
    })
  }
})

initDateRange('上险数', true)
getDictsData()
// setSubMarket1Mod(params.subMarket1)
</script>

<style scoped lang="scss">
.search-form {
  margin-bottom: 0;
}
.segmented {
  margin: 10px 0;
}
</style>
