<template>
  <!-- 友商数 -->
  <div class="wrap">
    <div class="table-list">
      <commonForm
        @reset="resetSearch"
        @search="handleSearch"
        @setting="table.select.flag = !table.select.flag"
      />
      <div class="table-list__control">
        <el-button type="primary" icon="Plus" @click="handleImport(true)">全量导入</el-button>
        <el-button type="primary" icon="Plus" @click="handleImport()">增量导入</el-button>
        <el-button type="primary" icon="Download" @click="downloadExcel">导出</el-button>
      </div>
      <el-table
        v-loading="loading.table"
        height="100%"
        :data="table.list"
        class="table-list__content"
      >
        <template v-for="i in table.listColumn">
          <el-table-column :label="i.label" :prop="i.prop" :min-width="i.width" show-overflow-tooltip />
        </template>
      </el-table>
      <BiPagination
        :total="table.total"
        v-model:page="table.page"
        v-model:limit="table.limit"
        @pagination="getList"
      />
      <uploadObs ref="refImport" url="/intelligence/competitor/increment/asyncImportData" @success="getList" />
      <uploadObs ref="refFullImport" url="/intelligence/competitor/full/asyncImportData" @success="getList" />

      <formSelectDialog v-model="table.select.flag" :allFormData="yss" />
      <downloadListDialog
        v-model="table.download.flag"
        :params="{ module: '0' }"
        downloadUrl="fileDownload/download"
      />
    </div>
  </div>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import uploadObs from '../industry/components/uploadObs.vue'
import formSelectDialog from '../industry/components/formSelectDialog.vue'
import commonForm from '../industry/components/commonForm.vue'
import downloadListDialog from '../industry/components/downloadListDialog.vue'
// 数据库管理-行业数据-友商数
import { competitorList } from '@/api/database/business'

import useTableData from '../industry/components/tablehooks.js'
import useFormKeyHooks from '../industry/components/allFormKeyHooks.js'

const refImport = ref(null) // 上传对象
const refFullImport = ref(null) // 上传对象
// 搜索参数
const defaultFormJson = {
  dataRange: '日期范围'
}
const { yss } = useFormKeyHooks()

const {
  table,
  loading,
  getList,
  handleSearch,
  resetSearch,
  toggleDownloadList,
  downloadExcel,
  mixinsParamsJson,
  setListColumn
} = useTableData({
  api: competitorList,
  download: {
    url: 'intelligence/competitor/exportData',
    name: '友商数'
  }
})

/** 上传按钮操详情作 */
function handleImport(isFull) {
  if (isFull) {
    refFullImport.value.show()
  } else {
    refImport.value.show()
  }
}

mixinsParamsJson(defaultFormJson)
setListColumn(yss)
getList()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.table-list {
  // height: calc($bi-main-height - 60px);
  height: 100%;
  padding: 0;
  .table-list__search {
    padding: 0;
  }
  .table-list__content {
    border-radius: 8px;
  }
}

.wrap {
  padding: $bi-layout-margin;
  height: $bi-main-height;
}
</style>
