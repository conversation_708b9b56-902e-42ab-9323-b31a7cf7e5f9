<template>
  <el-dialog
    v-model="modelValue"
    :close-on-click-modal="false"
    title="搜索条件展示选择"
    width="500px"
    top="2vh"
    append-to-body
  >
    <el-transfer
      v-model="table.select.value"
      :data="formData"
      :titles="['隐藏', '显示']"
      class="bi-transfer"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="toggleSuccess">确定</el-button>
        <el-button @click="modelValue = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
const emit = defineEmits(['success'])
const props = defineProps({
  allFormData: {
    type: Array,
    default: () => []
  }
})

const formData = ref(props.allFormData.filter(e => e.form !== false))

const modelValue = defineModel({ require: true })
const table = inject('table')

function toggleSuccess() {
  const arrayKey = table.isArrayParamsKey // 是数组的值
  const params = {}
  const paramsArray = {}
  const allDataString = table.select.value.join()
  formData.forEach(el => {
    if (allDataString.indexOf(el.key) !== -1) {
      params[el.key] = arrayKey.indexOf(el.key) !== -1 ? [] : ''
      paramsArray[el.key] = el.label
    }
  })
  table.formJson = paramsArray
  table.queryParams = { ...table.defaultParams, ...params }
  modelValue.value = false
}
</script>
<style scoped lang="scss">
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.bi-transfer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
