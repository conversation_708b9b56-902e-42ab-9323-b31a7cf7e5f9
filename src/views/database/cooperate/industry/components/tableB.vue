<template>
  <!-- 海关数 -->
  <div class="table-list">
    <commonForm
      :formJson="table.formJson"
      :form="table.queryParams"
      @reset="resetSearch"
      @search="handleSearch"
      @setting="table.select.flag = !table.select.flag"
    />
    <div class="table-list__control">
      <el-button type="primary" icon="Plus" @click="handleImport(true)">全量导入</el-button>
      <el-button type="primary" icon="Plus" @click="handleImport()">增量导入</el-button>
      <el-button type="primary" icon="Download" @click="downloadExcel">导出</el-button>
    </div>
    <el-table
      v-loading="loading.table"
      height="100%"
      :data="table.list"
      class="table-list__content"
    >
<!--      <el-table-column-->
<!--        label="主机厂"-->
<!--        prop="manufacturerName"-->
<!--        min-width="130"-->
<!--        show-overflow-tooltip-->
<!--      />-->
      <template v-for="i in table.listColumn">
        <el-table-column :label="i.label" :prop="i.prop" :min-width="i.width" show-overflow-tooltip />
      </template>

      <!-- <el-table-column label="年份" prop="year" width="60" />
      <el-table-column label="月份" prop="month" width="60" />
      <el-table-column label="季度" prop="quarter" />
      <el-table-column label="发动机厂商" prop="engineManufacturer" min-width="130" />
      <el-table-column label="法定数量" prop="legalQuantity" />
      <el-table-column label="板块" prop="businessSegment" />
      <el-table-column label="板块2" prop="businessSegment2" />
      <el-table-column label="板块3" prop="businessSegment3" />
      <el-table-column label="细分市场一" prop="submarket1" min-width="130" />
      <el-table-column label="区域细分" prop="regionSubdivision" />
      <el-table-column label="区域细分2" prop="regionSubdivision2" min-width="130" /> -->
    </el-table>
    <BiPagination
      :total="table.total"
      v-model:page="table.page"
      v-model:limit="table.limit"
      @pagination="getList"
    />
    <uploadObs ref="refImport" url="/intelligence/customs/increment/asyncImportData" @success="getList" />
    <uploadObs ref="refFullImport" url="/intelligence/customs/full/asyncImportData" @success="getList" />
    <formSelectDialog v-model="table.select.flag" :allFormData="hgs" />
    <downloadListDialog
      v-model="table.download.flag"
      :params="{ module: '1' }"
      downloadUrl="intelligence/fileDownload/download"
    />
  </div>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import uploadObs from './uploadObs.vue'
import formSelectDialog from './formSelectDialog.vue'
import commonForm from './commonForm.vue'
import downloadListDialog from './downloadListDialog.vue'
// 数据库管理-行业数据-海关数列表
import { customsList } from '@/api/database/business'

import useTableData from './tablehooks.js'
import useFormKeyHooks from './allFormKeyHooks.js'

const refImport = ref(null) // 上传对象
const refFullImport = ref(null) // 上传对象
// 搜索参数
const defaultFormJson = {
  dataRange: '日期范围'
}
const { hgs } = useFormKeyHooks()

const {
  table,
  loading,
  getList,
  handleSearch,
  resetSearch,
  toggleDownloadList,
  downloadExcel,
  mixinsParamsJson,
  setListColumn
} = useTableData({
  api: customsList,
  download: {
    url: 'intelligence/customs/exportData',
    name: '海关数'
  }
})

/** 上传按钮操详情作 */
function handleImport(isFull) {
  if (isFull) {
    refFullImport.value.show()
  } else {
    refImport.value.show()
  }
}

mixinsParamsJson(defaultFormJson)
setListColumn(hgs)
getList()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.table-list {
  // height: calc($bi-main-height - 60px);
  height: 100%;
  padding: 0;
  .table-list__content {
    border-radius: 8px;
  }
}
</style>
