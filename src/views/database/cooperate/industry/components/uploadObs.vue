<template>
  <el-dialog title="导入" v-model="visible" append-to-body :before-close="beforeClose">
    <el-form ref="refForm" :model="data.queryParams" :rules="data.rules" label-width="0">
      <el-form-item prop="fileList">
        <el-upload
            ref="refUpload"
            v-model:file-list="data.queryParams.fileList"
            :limit="1"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            :on-success="onSuccess"
            :auto-upload="false"
            :hide-upload-list="true"
            :data="data.queryParams"
            :disabled="loading.submit"
            drag
            class="bi-uploads"
            style="width: 100%"
        >
          <el-icon class="el-icon--upload">
            <upload-filled/>
          </el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </el-upload>

        <el-row style="width: 100%;">
          <el-col :span="21">
            <el-progress v-if="progress.data > 0" :percentage="progress.data"
                         :status="progress.status"></el-progress>
          </el-col>
          <el-col :span="3">
            <div class="el-progress__text" style="font-size: 14.4px;padding-left: 16px;">
              <span>{{ progress.speed }}</span>
            </div>
          </el-col>
        </el-row>

      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading.submit" @click="submitForm">上 传</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {ElMessage, ElMessageBox, genFileId} from 'element-plus'
import request, {uploadOBS} from "../../../../../utils/request";
import {ref} from "vue";

const emit = defineEmits(['success'])
const props = defineProps({
  url: {
    type: String,
    required: true,
    default: ''
  }
})

const refUpload = ref(null) // upload实例
const refForm = ref(null)
const progress = ref({
  data: 0,
  status: '',
  speed: ''
})
const progressStatus = ref(null)
const data = reactive({
  queryParams: {
    updateSupport: 1,
    fileList: []
  },
  rules: {
    fileList: [{required: true, message: '上传Excel不能为空', trigger: 'change'}]
  }
})
const loading = reactive({
  submit: false
})
const visible = ref(false)

/** 详情按钮操作 */
function show() {
  visible.value = true
}

/** 取消按钮 */
function cancel() {
  beforeClose()
  visible.value = false
}

const beforeClose = async () => {
  refUpload.value.clearFiles()
  refForm.value.resetFields()
  await nextTick()
}
// 设置只能上传一个
const handleExceed = files => {
  refUpload.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  refUpload.value.handleStart(file)
}

/** 提交按钮 */
function submitForm() {
  progress.value = {
    data: 0,
    status: null,
    speed: ''
  }
  if (loading.submit) return
  refForm.value.validate(valid => {
    if (valid) {
      loading.submit = true
      uploadOBS("database", data.queryParams.fileList[0].raw, (complete, speed) => {
        progress.value.data = complete
        progress.value.speed = speed
      }).then(filePath => {
        console.log("===============filePath: " + filePath)
        return request({
          url: props.url,
          method: 'post',
          data: {
            filePath
          }
        })
      }).then(() => {
        progress.value.status = 'success'
        progress.value.speed = ''
        ElMessageBox.alert(`文件上传成功！正在后台执行导入数据中...`, {
          dangerouslyUseHTMLString: true
        })
        emit('success', true)
        loading.submit = false
      }).catch(err => {
        progress.value.status = 'exception'
        progress.value.speed = ''
      })
    }
  })
}

const onSuccess = res => {
  if (res.code === 200) {
    cancel()
    ElMessageBox.alert(`文件上传成功！正在执行异步导入数据中...`, {
      dangerouslyUseHTMLString: true
    })
    emit('success', true)
  } else {
    beforeClose()
    ElMessage({
      message: `${res.code}-${res.msg}`,
      type: 'warning'
    })
  }
  loading.submit = false
}

defineExpose({
  show
})
</script>
