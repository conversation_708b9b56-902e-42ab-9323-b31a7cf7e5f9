// 数据库管理-行业数据-表格配置
import {
  competitorList,
  serverDataDelete
} from '@/api/database/serviceData'

/**
 * 表格配置对象
 * @param {Function} api - API调用函数
 * @param {Object} download - 下载配置 {url, name}
 * @param {Object} uploadUrls - 上传URL配置 {increment, full}
 * @param {string} formDataKey - 表单数据在useFormKeyHooks中的key
 * @param {string} title - 表格标题
 * @param {string} module - 模块参数，默认为'1'
 */

export const TABLE_CONFIGS = {
  // 中内协
  tableA: {
    api: competitorList,
    download: {
      url: 'intelligence/cnEnginOrg/exportData',
      name: '中内协'
    },
    uploadUrls: {
      increment: '/intelligence/cnEnginOrg/increment/asyncImportData',
      full: '/intelligence/cnEnginOrg/full/asyncImportData'
    },
    formDataKey: 'znx',
    title: '中内协',
    module: '1',
    tableKey: 'tableA'
  },

  // 海关数
  tableB: {
    api: competitorList,
    download: {
      url: 'intelligence/customs/exportData',
      name: '海关数'
    },
    uploadUrls: {
      increment: '/intelligence/customs/increment/asyncImportData',
      full: '/intelligence/customs/full/asyncImportData'
    },
    formDataKey: 'hgs',
    title: '海关数',
    module: '1',
    tableKey: 'tableB'
  },

  // 装机数
  tableC: {
    api: competitorList,
    download: {
      url: 'intelligence/install/exportData',
      name: '装机数'
    },
    uploadUrls: {
      increment: '/intelligence/install/increment/asyncImportData',
      full: '/intelligence/install/full/asyncImportData'
    },
    formDataKey: 'zjs',
    title: '装机数',
    module: '1',
    tableKey: 'tableC'
  },

  // 上险数
  tableD: {
    api: competitorList,
    download: {
      url: 'intelligence/insure/exportData',
      name: '上险数'
    },
    uploadUrls: {
      increment: '/intelligence/insure/increment/asyncImportData',
      full: '/intelligence/insure/full/asyncImportData'
    },
    formDataKey: 'sxs',
    title: '上险数',
    module: '1',
    tableKey: 'tableD'
  },

  // 货运新增
  tableE: {
    api: competitorList,
    download: {
      url: 'intelligence/corNewFreight/exportData',
      name: '货运新增'
    },
    uploadUrls: {
      increment: '/intelligence/corNewFreight/increment/asyncImportData',
      full: '/intelligence/corNewFreight/full/asyncImportData'
    },
    formDataKey: 'hyxz',
    title: '货运新增',
    module: '1',
    tableKey: 'tableE'
  },

  // 船电数
  tableF: {
    api: competitorList,
    download: {
      url: 'intelligence/ship/exportData',
      name: '船电数'
    },
    uploadUrls: {
      increment: '/intelligence/ship/increment/asyncImportData',
      full: '/intelligence/ship/full/asyncImportData'
    },
    formDataKey: 'cds',
    title: '船电数',
    module: '1',
    tableKey: 'tableF'
  },

  // 流向数
  tableG: {
    api: competitorList,
    download: {
      url: 'intelligence/flowData/exportData',
      name: '流向数'
    },
    uploadUrls: {
      increment: '/intelligence/flowData/increment/asyncImportData',
      full: '/intelligence/flowData/full/asyncImportData'
    },
    formDataKey: 'lxs',
    title: '流向数',
    module: '1',
    tableKey: 'tableG'
  }
}

/**
 * 获取表格配置
 * @param {string} tableKey - 表格key
 * @returns {Object} 表格配置对象
 */
export function getTableConfig(tableKey) {
  const config = TABLE_CONFIGS[tableKey]
  if (!config) {
    throw new Error(`Table config not found for key: ${tableKey}`)
  }
  return config
}

/**
 * 获取所有表格配置
 * @returns {Object} 所有表格配置
 */
export function getAllTableConfigs() {
  return TABLE_CONFIGS
}
