<template>
  <el-dialog
    v-model="modelValue"
    :close-on-click-modal="false"
    title="下载列表"
    width="80vw"
    top="2vh"
    append-to-body
  >
    <el-table v-loading="loading.table" height="100%" :data="data.list" class="table-list__content">
      <el-table-column label="文件名" prop="fileName" min-width="150" show-overflow-tooltip />
      <el-table-column label="创建日期" prop="createTm" width="180" />
      <el-table-column label="操作" width="80">
        <template #default="{ row }">
          <el-button type="primary" text style="padding: 0" @click="downloadItemExcel(row)"
            >下载</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <BiPagination
      :total="data.total"
      v-model:page="data.params.pageNum"
      v-model:limit="data.params.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>
<script setup>
import BiPagination from '@/views/components/BiPagination.vue'

import { fileDownloadList } from '@/api/intelligence/fileDownload.js'

const emit = defineEmits(['success'])
const { proxy } = getCurrentInstance()
const props = defineProps({
  params: {
    type: Object,
    default: () => ({
      module: ''
    })
  },
  downloadUrl: {
    type: String,
    default: 'fileDownload/download'
  }
})
const data = reactive({
  params: {
    module: '',
    pageNum: 1,
    pageSize: 15
  },
  list: [],
  total: 0
})
const loading = reactive({
  table: false
})
const modelValue = defineModel({ require: true })

watch(modelValue, val => {
  if (val) {
    getList()
  } else {
    data.params = {
      module: '',
      pageNum: 1,
      pageSize: 15
    }
    data.list = []
    data.total = 0
  }
})
/** 查询列表 */
const getList = async () => {
  if (loading.table) return
  loading.table = true
  const params = JSON.parse(JSON.stringify(data.params))
  params.module = props.params.module
  console.log('params', params)
  const res = await fileDownloadList(params).catch(e => e)
  if (res.code !== 200) {
    loading.table = false
    return
  }
  console.log('res', res)
  data.list = res.rows
  data.total = res.total
  loading.table = false
}

// 导出文件
const downloadItemExcel = ev => {
  const params = {}
  // proxy.downloadJSON(`intelligence/fileDownload/download?id=${ev.id}`, params, ev.fileName)
  proxy.downloadJSON(`${props.downloadUrl}?id=${ev.id}`, params, ev.fileName)
}

function toggleSuccess() {
  modelValue.value = false
}
</script>
<style scoped lang="scss">
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.bi-transfer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
