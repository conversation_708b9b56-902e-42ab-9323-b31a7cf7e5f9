import { reactive, provide } from 'vue'
import dayjs from "dayjs";
export default function useTableData({ api, download }) {
  const { proxy } = getCurrentInstance()
  // formJson = {
  //   dataRange: '日期范围'
  // }
  // select: {
  //     value: ['dataRange'] // 选中的搜索
  // }
  const yearMonthColumn = ['dataRange', 'yearMonth', 'yearMonthDay'] // 替换成年月显示列表的值
  const table = reactive({
    refForm: null,
    defaultParams: {}, // 默认的搜索条件
    isArrayParamsKey: ['dataRange', 'yearMonth', 'yearMonthDay'], // 是数组的值
    formJson: {},
    queryParams: {},
    page: 1,
    limit: 10,
    listColumn: [], // 列表展示的列
    list: [],
    total: 0,
    select: {
      flag: false,
      data: [],
      value: [] // 选中的搜索
    },
    config: [],
    download: {
      flag: false,
    }
  })
  provide('table', table)
  const loading = reactive({
    table: false
  })
  function getQueryData() {
    const queryData = JSON.parse(JSON.stringify(table.queryParams))
    const params = []
    for (let key in queryData) {
      const config = table.config.find(e => e.key === key)
      if (!config) {
        continue
      }
      const value = queryData[key];
      if (!value || value.length === 0) {
        continue
      }
      if (config.dataType === 'date') {
        for (let valueKey in config.setValue) {
          params.push({
            name: valueKey,
            op: 'between',
            values: [dayjs(value[0]).format(config.setValue[valueKey]), dayjs(value[1]).format(config.setValue[valueKey])],
          })
        }
      } else {
        params.push({
          name: key,
          op: 'like',
          value: value
        })
      }
    }
    console.log('queryData', queryData)
    console.log('params', params)
    return params
  }
  /** 查询列表 */
  const getList = async () => {
    if (loading.table) return
    loading.table = true
    const res = await api({
      page: table.page,
      limit: table.limit,
      params: getQueryData(),
    }).catch(e => e)
    if (res.code !== 200) {
      loading.table = false
      return
    }
    table.list = res.data.table
    table.total = res.data.total
    flexColumnWidth()
    loading.table = false
  }

  /** 搜索按钮操作 */
  const handleSearch = async () => {
    table.page = 1
    await getList()
  }

  /** 重置按钮操作 */
  function resetSearch() {
    if (table.refForm && table.refForm.resetFields) table.refForm.resetFields()
    handleSearch()
  }
  // 导出文件
  const downloadExcel = () => {
    if (table.queryParams.dataRange && table.queryParams.dataRange.length === 0) {
      proxy.$modal.msgError('年月必须输入')
      return
    }
    const params = {
      params: getQueryData()
    }
    let time = table.queryParams.dataRange.join('至') || ''
    proxy.downloadJSON(
      download.url,
      params,
      `${time}_${download.name}_${new Date().getTime()}.xlsx`
    )
  }

  // 点击下载显示下载列表
  function toggleDownloadList(ev) {
    table.download.flag = true
  }
  // 合并搜索参数进入queryParams
  function mixinsParams(params) {
    table.queryParams = { ...table.defaultParams, ...params }
  }
  function mixinsParamsJson(formJson) {
    const isArrayParamsKey = table.isArrayParamsKey
    const params = {}
    const selectValue = []
    for (let i in formJson) {
      if (isArrayParamsKey.indexOf(i) !== -1) {
        params[i] = []
      } else {
        params[i] = ''
      }
      selectValue.push(i)
    }
    table.queryParams = { ...table.defaultParams, ...params }
    table.formJson = formJson
    table.select.value = selectValue
  }
  // 设置列表展示的数据
  function setListColumn(list) {
    console.log('list', list)
    table.config = list.value
    const listColumn = []
    list.value.forEach(el => {
      if (el.table !== false) {
        const prop = el.databaseKey ? el.databaseKey : el.key
        listColumn.push({
          label: el.label,
          width: el.width,
          prop
        })
      }
    })
    table.listColumn = listColumn
  }

  /**
   * 遍历列的所有内容，获取最宽一列的宽度
   * @param arr
   */
  function getMaxLength (arr) {
    return arr.reduce((acc, item) => {
      if (item) {
        const calcLen = getTextWidth(item)
        if (acc < calcLen) {
          acc = calcLen
        }
      }
      return acc
    }, 0)
  }
  /**
   * 使用span标签包裹内容，然后计算span的宽度 width： px
   */
  function getTextWidth (str) {
    let width = 0
    const html = document.createElement('span')
    html.innerText = str
    html.className = 'getTextWidth el-table--large'
    document.querySelector('body').appendChild(html)
    width = document.querySelector('.getTextWidth').offsetWidth
    document.querySelector('.getTextWidth').remove()
    return width
  }
  /**
   * el-table-column 自适应列宽
   */
  function flexColumnWidth () {
    // console.log('label', label)
    // console.log('prop', prop)
    table.listColumn.forEach((item) => {
      // 1.获取该列的所有数据
      const arr = table.list.map(x => x[item.prop])
      // 把每列的表头也加进去算
      arr.push(item.label)
      // 2.计算每列内容最大的宽度 + 表格的内间距（依据实际情况而定）
      const width = getMaxLength(arr) + 40;
      item.width = (width > 200 ? 200 : width) + 'px'
    })
  }

  return {
    table,
    loading,
    getList,
    handleSearch,
    resetSearch,
    toggleDownloadList,
    downloadExcel,
    mixinsParamsJson,
    setListColumn
  }
}
