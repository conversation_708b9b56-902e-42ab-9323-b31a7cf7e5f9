# 删除功能使用说明

## 功能概述

已为所有业务数据表格组件添加了删除功能，用户可以通过日期范围选择来删除指定时间段的数据。

## 功能特点

1. **日期范围选择**：支持按月份范围选择要删除的数据
2. **二次确认**：删除前会弹出确认对话框，防止误操作
3. **统一接口**：所有表格使用统一的删除接口 `/service-data/deleteData`
4. **自动刷新**：删除成功后自动刷新表格数据

## 使用方法

### 1. 触发删除功能

在父组件中调用表格组件的 `handleDelete` 方法：

```javascript
// 获取表格组件引用
const tableRef = ref(null)

// 调用删除功能
const onDelete = () => {
  tableRef.value.handleDelete()
}
```

### 2. 删除对话框操作

1. 点击删除按钮后，会弹出删除对话框
2. 选择要删除数据的日期范围（月份范围）
3. 点击"确认删除"按钮
4. 系统会弹出二次确认对话框
5. 确认后执行删除操作

### 3. 接口参数

删除接口会发送以下参数：

```javascript
{
  tableKey: 'tableA',     // 表格标识（tableA-tableG）
  startYear: 2024,        // 开始年份
  endYear: 2024,          // 结束年份  
  startMonth: 1,          // 开始月份
  endMonth: 12            // 结束月份
}
```

## 表格标识映射

| 表格组件 | tableKey | 表格名称 |
|----------|----------|----------|
| tableA   | tableA   | 中内协   |
| tableB   | tableB   | 海关数   |
| tableC   | tableC   | 装机数   |
| tableD   | tableD   | 上险数   |
| tableE   | tableE   | 货运新增 |
| tableF   | tableF   | 船电数   |
| tableG   | tableG   | 流向数   |

## 完整示例

```vue
<template>
  <div>
    <!-- 表格组件 -->
    <BaseBusinessTable 
      ref="tableRef"
      :config="tableConfig" 
    />
    
    <!-- 删除按钮 -->
    <el-button 
      type="danger" 
      @click="handleDelete"
    >
      删除数据
    </el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BaseBusinessTable from './BaseBusinessTable.vue'
import { getTableConfig } from './tableConfigs.js'

const tableRef = ref(null)
const tableConfig = getTableConfig('tableA')

const handleDelete = () => {
  tableRef.value.handleDelete()
}
</script>
```

## 安全提示

1. **谨慎操作**：删除操作不可恢复，请谨慎使用
2. **权限控制**：建议在后端接口中添加权限验证
3. **数据备份**：重要数据删除前建议先备份
4. **日志记录**：建议在后端记录删除操作日志

## 错误处理

系统会自动处理以下错误情况：

1. **网络错误**：显示网络连接异常提示
2. **服务器错误**：显示服务器返回的错误信息
3. **参数错误**：表单验证失败时显示相应提示
4. **权限错误**：显示权限不足提示

## 自定义配置

如需修改删除功能的行为，可以在以下文件中进行配置：

- `deleteDialog.vue`：修改对话框样式和验证规则
- `BaseBusinessTable.vue`：修改删除按钮的触发逻辑
- `tableConfigs.js`：修改表格配置信息
