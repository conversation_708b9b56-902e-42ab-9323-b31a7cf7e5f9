<template>
  <!-- 中内协 -->
  <div class="table-list">
    <commonForm
      @reset="resetSearch"
      @search="handleSearch"
      @setting="table.select.flag = !table.select.flag"
    />
    <!-- <div class="table-list__control">
      <el-button type="primary" icon="Plus" @click="handleImport(true)">全量导入</el-button>
      <el-button type="primary" icon="Plus" @click="handleImport()">增量导入</el-button>
      <el-button type="primary" icon="Download" @click="downloadExcel">导出</el-button>
    </div> -->
    <el-table
      v-loading="loading.table"
      height="100%"
      :data="table.list"
      class="table-list__content"
    >
<!--      <el-table-column label="企业名称" prop="companyName" min-width="150" show-overflow-tooltip />-->
      <template v-for="i in table.listColumn">
        <el-table-column :label="i.label" :prop="i.prop" :min-width="i.width" show-overflow-tooltip />
      </template>
<!--      <el-table-column label="本期止累计" prop="cumulativeToDate" />-->
<!--      <el-table-column label="同期止累计" prop="cumulativeSamePeriodLastYear" />-->
<!--      <el-table-column label="比上月增长%" prop="growthMonthlyPercentage" />-->
<!--      <el-table-column label="比同期增长%" prop="growthAnnualPercentage" />-->
<!--      <el-table-column label="比同期累计增长" prop="growthCumulative" />-->
    </el-table>
    <BiPagination
      :total="table.total"
      v-model:page="table.page"
      v-model:limit="table.limit"
      @pagination="getList"
    />
    <uploadObs ref="refImport" url="/intelligence/cnEnginOrg/increment/asyncImportData" @success="getList" />
    <uploadObs ref="refFullImport" url="/intelligence/cnEnginOrg/full/asyncImportData" @success="getList" />
    <formSelectDialog v-model="table.select.flag" :allFormData="znx" />
    <downloadListDialog
      v-model="table.download.flag"
      :params="{ module: '1' }"
      downloadUrl="intelligence/fileDownload/download"
    />
  </div>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import uploadObs from './uploadObs.vue'
import formSelectDialog from './formSelectDialog.vue'
import commonForm from './commonForm.vue'
import downloadListDialog from './downloadListDialog.vue'
// 数据库管理-行业数据-中内协列表
import { cnEnginOrgList } from '@/api/database/business'

import useTableData from './tablehooks.js'
import useFormKeyHooks from './allFormKeyHooks.js'

const refImport = ref(null) // 上传对象
const refFullImport = ref(null) // 上传对象
// 搜索参数
const defaultFormJson = {
  dataRange: '日期范围'
}
const { znx } = useFormKeyHooks()

const {
  table,
  loading,
  getList,
  handleSearch,
  resetSearch,
  toggleDownloadList,
  downloadExcel,
  mixinsParamsJson,
  setListColumn
} = useTableData({
  api: cnEnginOrgList,
  download: {
    url: 'intelligence/cnEnginOrg/exportData',
    name: '中内协'
  }
})

/** 上传按钮操详情作 */
function handleImport(isFull) {
  if (isFull) {
    refFullImport.value.show()
  } else {
    refImport.value.show()
  }
}

mixinsParamsJson(defaultFormJson)
setListColumn(znx)
getList()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.table-list {
  // height: calc($bi-main-height - 60px);
  height: 100%;
  padding: 0;

  .table-list__content {
    border-radius: 8px;
  }
}
</style>
