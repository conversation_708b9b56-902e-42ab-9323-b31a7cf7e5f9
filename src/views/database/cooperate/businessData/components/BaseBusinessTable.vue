<template>
  <div class="table-list">

    <commonForm
      :formJson="table.formJson"
      :form="table.queryParams"
      @reset="resetSearch"
      @search="handleSearch"
      @setting="table.select.flag = !table.select.flag"
      @delete="handleDelete"
    />
    
    <el-table
      v-loading="loading.table"
      height="100%"
      :data="table.list"
      class="table-list__content"
    >
      <template v-for="i in table.listColumn" :key="i.prop">
        <el-table-column 
          :label="i.label" 
          :prop="i.prop" 
          :min-width="i.width" 
          show-overflow-tooltip 
        />
      </template>
    </el-table>
    
    <BiPagination
      :total="table.total"
      v-model:page="table.page"
      v-model:limit="table.limit"
      @pagination="getList"
    />
    
    <uploadObs 
      ref="refImport" 
      :url="config.uploadUrls.increment" 
      @success="getList" 
    />
    <uploadObs 
      ref="refFullImport" 
      :url="config.uploadUrls.full" 
      @success="getList" 
    />
    
    <formSelectDialog 
      v-model="table.select.flag" 
      :allFormData="formData" 
    />
    
    <downloadListDialog
      v-model="table.download.flag"
      :params="{ module: config.module || '1' }"
      downloadUrl="intelligence/fileDownload/download"
    />

    <deleteDialog
      v-model="deleteDialogVisible"
      :formDataKey="config.formDataKey"
      :tableName="config.title"
      @success="handleDeleteSuccess"
    />
  </div>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import uploadObs from './uploadObs.vue'
import formSelectDialog from './formSelectDialog.vue'
import commonForm from './commonForm.vue'
import downloadListDialog from './downloadListDialog.vue'
import deleteDialog from './deleteDialog.vue'
import useTableData from './tablehooks.js'
import useFormKeyHooks from './allFormKeyHooks.js'

// 定义props
const props = defineProps({
  config: {
    type: Object,
    required: true,
    validator: (config) => {
      return config.api && 
             config.download && 
             config.uploadUrls && 
             config.formDataKey
    }
  }
})

const refImport = ref(null)
const refFullImport = ref(null)
const deleteDialogVisible = ref(false)

// 获取表单数据
const formKeyHooks = useFormKeyHooks()
const formData = formKeyHooks[props.config.formDataKey]

// 搜索参数
const defaultFormJson = {
  dataRange: '日期范围'
}
console.log('参数',props.config)
// 使用表格数据hook
const {
  table,
  loading,
  getList,
  handleSearch,
  resetSearch,
  downloadExcel,
  mixinsParamsJson,
  setListColumn
} = useTableData({
  api: props.config.api,
  download: props.config.download,
  tableKey:props.config.formDataKey,
})

/** 上传按钮操作 */
function handleImport(isFull) {
  if (isFull) {
    refFullImport.value.show()
  } else {
    refImport.value.show()
  }
}

/** 删除按钮操作 */
function handleDelete() {
  deleteDialogVisible.value = true
}

/** 删除成功回调 */
function handleDeleteSuccess() {
  // 重新加载数据
  getList()
}

// 初始化
mixinsParamsJson(defaultFormJson)
setListColumn(formData)
getList()

// 暴露方法给父组件使用
defineExpose({
  getList,
  handleSearch,
  resetSearch,
  downloadExcel,
  handleImport,
  handleDelete
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

.table-list {
  height: 100%;
  padding: 0;

  .table-list__content {
    border-radius: 8px;
  }
}
</style>
