import {ref} from 'vue'

export default function useFormKeyHooks() {
    // 中内协
    const znx = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            dataType: 'date',
            setValue: {
                year: 'YYYY年',
                month: 'MM月'
            },
            table: false
        },
        {
            label: "企业名称",
            key: "companyName"
        },
        {
            label: "月份",
            key: "month",
            form: false
        },
        {
            label: "年份",
            key: "year",
            form: false
        },
        {
            label: "本月完成",
            key: "currentMonthCompletion"
        },
        {
            label: "本期止累计",
            key: "cumulativeToDate"
        },
        {
            label: "同期止累计",
            key: "cumulativeSamePeriodLastYear"
        },
        {
            label: "比上月增长%",
            key: "growthMonthlyPercentage"
        },
        {
            label: "比同期增长%",
            key: "growthAnnualPercentage"
        },
        {
            label: "比同期累计增长",
            key: "growthCumulative"
        },
        {
            label: "缸数",
            key: "cylinderCount"
        },
        {
            label: "用途",
            key: "usage"
        },
        {
            label: "板块",
            key: "sector"
        },
        {
            label: "企业简称",
            key: "companyAbbr"
        },
        {
            label: "企业简称2",
            key: "companyAbbr2"
        },
        {
            label: "板块细分",
            key: "sectorSubdivision"
        }])

    // 海关数
    const hgs = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            dataType: 'date',
            setValue: {
                year: 'YYYY',
                month: 'M'
            },
            table: false
        },
        {
            label: "制造商",
            key: "manufacturer",
            width: '150'
        },
        {
            label: "年份",
            key: "year",
            form: false
        },
        {
            label: "月份",
            key: "month",
            form: false
        },
        {
            label: "季度",
            key: "quarter"
        },
        {
            label: "商品编码",
            key: "productCode"
        },
        {
            label: "商品编码释义",
            key: "productDesc"
        },
        {
            label: "商品名称",
            key: "productName"
        },
        {
            label: "二手车",
            key: "usedCar"
        },
        {
            label: "法定数量",
            key: "legalQuantity"
        },
        {
            label: "法定单位",
            key: "legalUnit"
        },
        {
            label: "收发货企业名称",
            key: "consignor"
        },
        {
            label: "经营单位企业名称",
            key: "operator"
        },
        {
            label: "申报单位名称",
            key: "declarant"
        },
        {
            label: "车辆型号",
            key: "vehicleModel"
        },
        {
            label: "规格型号",
            key: "specification"
        },
        {
            label: "发动机厂商",
            key: "engineManufacturer"
        },
        {
            label: "发动机型号（可配）",
            key: "engineModel"
        },
        {
            label: "企业性质",
            key: "enterpriseNature"
        },
        {
            label: "成交方式",
            key: "transactionMethod"
        },
        {
            label: "单价",
            key: "unitPrice"
        },
        {
            label: "总价",
            key: "totalPrice"
        },
        {
            label: "境内货源地",
            key: "sourceLocation"
        },
        {
            label: "包装形式",
            key: "packageType"
        },
        {
            label: "贸易国别",
            key: "tradeCountry"
        },
        {
            label: "最终目的国",
            key: "finalDestination"
        },
        {
            label: "贸易方式",
            key: "tradeMode"
        },
        {
            label: "运输方式",
            key: "transportMode"
        },
        {
            label: "主管海关",
            key: "customs"
        },
        {
            label: "直属海关",
            key: "directCustoms"
        },
        {
            label: "指运港",
            key: "destinationPort"
        },
        {
            label: "进出口岸",
            key: "importExportPort"
        },
        {
            label: "车辆类别",
            key: "vehicleCategory"
        },
        {
            label: "车辆类别2",
            key: "vehicleCategory2"
        },
        {
            label: "排量（Ml）",
            key: "displacementMl"
        },
        {
            label: "总质量（Kg）",
            key: "totalWeightKg"
        },
        {
            label: "区域",
            key: "region"
        },
        {
            label: "海外办事处",
            key: "overseasOffice"
        },
        {
            label: "新大区",
            key: "newBigRegion"
        },
        {
            label: "新国区",
            key: "newLocalRegion"
        },
        {
            label: "区域细分",
            key: "regionSubdivision"
        },
        {
            label: "区域细分2",
            key: "regionSubdivision2"
        },
        {
            label: "板块",
            key: "businessSegment"
        },
        {
            label: "销售结构1",
            key: "salesStructure1"
        },
        {
            label: "玉柴相关",
            key: "ycRelated"
        },
        {
            label: "板块2",
            key: "businessSegment2"
        },
        {
            label: "销售结构2",
            key: "salesStructure2"
        },
        {
            label: "板块3",
            key: "businessSegment3"
        },
        {
            label: "销售结构3",
            key: "salesStructure3"
        },
        {
            label: "制造商整合",
            key: "manufacturerName1"
        }
    ])

    // 装机数
    const zjs = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            dataType: 'date',
            setValue: {
                year: 'YYYY年',
                month: 'MM月'
            },
            table: false
        },
        {
            label: "年份",
            key: "year",
            form: false
        },
        {
            label: "月份",
            key: "month",
            form: false
        },
        {
            label: "旬度",
            key: "decade"
        },
        {
            label: "所属大区",
            key: "region"
        },
        {
            label: "主机厂",
            key: "manufacturer"
        },
        {
            label: "用途",
            key: "purpose"
        },
        {
            label: "整车车型",
            key: "vehiclemodel"
        },
        {
            label: "发动机厂家",
            key: "enginemanufacturer"
        },
        {
            label: "燃料种类",
            key: "fueltype"
        },
        {
            label: "机型",
            key: "enginemodel"
        },
        {
            label: "排放",
            key: "emission"
        },
        {
            label: "出口/国内",
            key: "exportdomestic"
        },
        {
            label: "板块",
            key: "sector"
        },
        {
            label: "装机数",
            key: "installationcount"
        },
        {
            label: "是否剔除统计",
            key: "excludefromstatistics"
        },
        {
            label: "季度",
            key: "quarter"
        },
        {
            label: "半年度",
            key: "semiannual"
        },
        {
            label: "板块1",
            key: "sector1"
        },
        {
            label: "细分市场二",
            key: "submarket2"
        },
        {
            label: "新用途1",
            key: "newpurpose1"
        },
        {
            label: "装机数量",
            key: "installedquantity"
        },
        {
            label: "自产/社会",
            key: "selfproducedsocial"
        },
        {
            label: "主机厂2",
            key: "manufacturer2"
        },
        {
            label: "细分市场一",
            key: "submarket1"
        },
        {
            label: "燃料种类1",
            key: "fueltype1"
        },
        {
            label: "机型1",
            key: "enginemodel1"
        },
        {
            label: "机型系列2",
            key: "enginemodelseries2"
        },
        {
            label: "排量",
            key: "displacement"
        },
        {
            label: "排量范围",
            key: "displacementrange"
        },
        {
            label: "发动机厂家1",
            key: "enginemanufacturer1"
        }
    ])

    // 上险数
    const sxs = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            dataType: 'date',
            setValue: {
                year: 'YYYY',
                month: 'M'
            },
            table: false
        },
        {
            label: "年份",
            key: "year",
            form: false
        },
        {
            label: "月份",
            key: "month",
            form: false
        },
        {
            label: "销售省份",
            key: "salesProvince"
        },
        {
            label: "销售城市",
            key: "salesCity"
        },
        {
            label: "销售区县",
            key: "salesCounty"
        },
        {
            label: "车辆型号",
            key: "vehicleModel"
        },
        {
            label: "整车企业(公告)",
            key: "manufacturerPublic"
        },
        {
            label: "整车企业(公告)简称",
            key: "manufacturerShort"
        },
        {
            label: "整车企业(实际生产)",
            key: "manufacturerActual"
        },
        {
            label: "整车品牌",
            key: "brand"
        },
        {
            label: "车辆类型",
            key: "vehicleType"
        },
        {
            label: "车辆名称",
            key: "vehicleName"
        },
        {
            label: "车型俗称",
            key: "modelNickname"
        },
        {
            label: "驾驶室型号",
            key: "cabModel"
        },
        {
            label: "车型大类",
            key: "vehicleCategory"
        },
        {
            label: "车型分类",
            key: "vehicleClassification"
        },
        {
            label: "驱动型式",
            key: "driveType"
        },
        {
            label: "轴数",
            key: "axleCount"
        },
        {
            label: "驱动轴数",
            key: "driveAxleCount"
        },
        {
            label: "轴距(mm)",
            key: "wheelbase"
        },
        {
            label: "车长(mm)",
            key: "vehicleLength"
        },
        {
            label: "车宽(mm)",
            key: "vehicleWidth"
        },
        {
            label: "车高(mm)",
            key: "vehicleHeight"
        },
        {
            label: "货箱长(mm)",
            key: "cargoLength"
        },
        {
            label: "货箱宽(mm)",
            key: "cargoWidth"
        },
        {
            label: "货箱高(mm)",
            key: "cargoHeight"
        },
        {
            label: "总质量(kg)",
            key: "totalWeight"
        },
        {
            label: "整备质量(kg)",
            key: "curbWeight"
        },
        {
            label: "额定载质量(kg)",
            key: "ratedLoadWeight"
        },
        {
            label: "准牵引质量(kg)",
            key: "maxTowingWeight"
        },
        {
            label: "额定载客",
            key: "ratedPassengerCount"
        },
        {
            label: "轮胎数",
            key: "tireCount"
        },
        {
            label: "轮胎规格",
            key: "tireSpecification"
        },
        {
            label: "端面宽度(mm)",
            key: "endFaceWidth"
        },
        {
            label: "外直径(mm)",
            key: "outerDiameter"
        },
        {
            label: "静半径(mm)",
            key: "staticRadius"
        },
        {
            label: "最高车速(km/h)",
            key: "maxSpeed"
        },
        {
            label: "底盘型号",
            key: "chassisModel"
        },
        {
            label: "底盘企业(公告)",
            key: "chassisManufacturerPublic"
        },
        {
            label: "底盘企业(公告)简称",
            key: "chassisManufacturerShort"
        },
        {
            label: "底盘企业(实际生产）",
            key: "chassisManufacturerActual"
        },
        {
            label: "底盘集团",
            key: "chassisGroup"
        },
        {
            label: "底盘省份",
            key: "chassisProvince"
        },
        {
            label: "底盘城市",
            key: "chassisCity"
        },
        {
            label: "底盘品牌",
            key: "chassisBrand"
        },
        {
            label: "底盘国产/进口",
            key: "chassisDomesticImport"
        },
        {
            label: "发动机型号",
            key: "engineModel"
        },
        {
            label: "发动机系列",
            key: "engineSeries"
        },
        {
            label: "发动机企业",
            key: "engineCompany"
        },
        {
            label: "发动机企业_简称",
            key: "engineCompanyShort"
        },
        {
            label: "燃料种类",
            key: "fuelType"
        },
        {
            label: "排量(ml)",
            key: "displacement"
        },
        {
            label: "气缸数",
            key: "cylinderCount"
        },
        {
            label: "功率(kW)",
            key: "power"
        },
        {
            label: "排放标准",
            key: "emissionStandard"
        },
        {
            label: "变速器厂家",
            key: "transmissionManufacturer"
        },
        {
            label: "变速器型号",
            key: "transmissionModel"
        },
        {
            label: "变速器类型",
            key: "transmissionType"
        },
        {
            label: "变速器速比",
            key: "transmissionRatio"
        },
        {
            label: "主减速比",
            key: "mainReductionRatio"
        },
        {
            label: "后桥型号",
            key: "rearAxleModel"
        },
        {
            label: "后桥厂家",
            key: "rearAxleManufacturer"
        },
        {
            label: "整车辅助制动",
            key: "vehicleAuxiliaryBrake"
        },
        {
            label: "马力",
            key: "horsepower"
        },
        {
            label: "整车企业",
            key: "vehicleCompany"
        },
        {
            label: "总质量区间",
            key: "totalWeightRange"
        },
        {
            label: "车长区间",
            key: "vehicleLengthRange"
        },
        {
            label: "燃料种类_玉柴",
            key: "fuelTypeYuchai"
        },
        {
            label: "板块",
            key: "segment"
        },
        {
            label: "板块细分",
            key: "segmentDetail"
        },
        {
            label: "细分市场1",
            key: "subMarket1"
        },
        {
            label: "细分市场2",
            key: "subMarket2"
        },
        {
            label: "细分市场3",
            key: "subMarket3"
        },
        {
            label: "微改区分",
            key: "microModification"
        },
        {
            label: "车型MN类划分",
            key: "modelMnClassification"
        },
        {
            label: "车型MN类细分",
            key: "modelMnClassificationDetail"
        },
        {
            label: "销量",
            key: "salesVolume"
        },
        {
            label: "底盘型号代号",
            key: "chassisModelCode"
        },
        {
            label: "大体车型",
            key: "generalModel"
        },
        {
            label: "重中轻-按总质量",
            key: "weightClassification"
        },
        {
            label: "马力段",
            key: "horsepowerSegment"
        },
        {
            label: "驱动形式",
            key: "driveForm"
        },
        {
            label: "板块",
            key: "segment2"
        },
        {
            label: "细分",
            key: "subSegment"
        },
        {
            label: "发动机厂名称",
            key: "engineFactoryName"
        },
        {
            label: "发动机厂全称",
            key: "engineFactoryFullName"
        },
        {
            label: "玉柴细分",
            key: "yuchaiDetail"
        },
        {
            label: "车长米数(厘米位四舍五入)",
            key: "vehicleLengthCm"
        },
        {
            label: "总质量区间2",
            key: "totalWeightRange2"
        },
        {
            label: "排放",
            key: "emission"
        },
        {
            label: "SCB燃料类型",
            key: "scbFuelType"
        },
        {
            label: "柴气电",
            key: "dieselGasElectric"
        },
        {
            label: "新能源",
            key: "newEnergy"
        },
        {
            label: "欧系轻客",
            key: "europeanLightTruck"
        },
        {
            label: "马力段-按细分及驱动",
            key: "horsepowerSegmentDetail"
        },
        {
            label: "中重卡、3.5吨轻卡",
            key: "mediumHeavyTruck"
        },
        {
            label: "柴气其他",
            key: "dieselGasOthers"
        },
        {
            label: "细分6-按车辆名称",
            key: "subSegment6"
        },
        {
            label: "产品细分1",
            key: "productSegment1"
        },
        {
            label: "产品细分2",
            key: "productSegment2"
        }
    ])

    // 货运新增
    const hyxz = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            dataType: 'date',
            setValue: {
                year: 'YYYY',
                month: 'M'
            },
            table: false
        },
        {
            label: "发动机厂家",
            key: "engineManufacturer"
        },
        {
            label: "年份",
            key: "year",
            form: false
        },
        {
            label: "月份",
            key: "month",
            form: false
        },
        // {
        //   label: "日",
        //   key: "day",
        //   form: false
        // },
        {
            label: "板块",
            key: "sector"
        },
        {
            label: "销售省份",
            key: "salesProvince"
        },
        {
            label: "销售城市",
            key: "salesCity"
        },
        {
            label: "销售区县",
            key: "salesCounty"
        },
        {
            label: "车辆型号",
            key: "vehicleModel"
        },
        {
            label: "整车企业(公告)",
            key: "manufacturerPublic"
        },
        {
            label: "整车企业(公告)简称",
            key: "manufacturerShort"
        },
        {
            label: "整车企业(实际生产)",
            key: "manufacturerActual"
        },
        {
            label: "整车品牌",
            key: "brand"
        },
        {
            label: "车辆类型",
            key: "vehicleType"
        },
        {
            label: "车辆名称",
            key: "vehicleName"
        },
        {
            label: "车型俗称",
            key: "modelNickname"
        },
        {
            label: "驾驶室型号",
            key: "cabModel"
        },
        {
            label: "车型大类",
            key: "vehicleCategory"
        },
        {
            label: "车型分类",
            key: "vehicleClassification"
        },
        {
            label: "驱动型式",
            key: "driveType"
        },
        {
            label: "轴数",
            key: "axleCount"
        },
        {
            label: "驱动轴数",
            key: "driveAxleCount"
        },
        {
            label: "轴距(mm)",
            key: "wheelbase"
        },
        {
            label: "车长(mm)",
            key: "vehicleLength"
        },
        {
            label: "车宽(mm)",
            key: "vehicleWidth"
        },
        {
            label: "车高(mm)",
            key: "vehicleHeight"
        },
        {
            label: "货箱长(mm)",
            key: "cargoLength"
        },
        {
            label: "货箱宽(mm)",
            key: "cargoWidth"
        },
        {
            label: "货箱高(mm)",
            key: "cargoHeight"
        },
        {
            label: "总质量(kg)",
            key: "totalWeight"
        },
        {
            label: "整备质量(kg)",
            key: "curbWeight"
        },
        {
            label: "额定载质量(kg)",
            key: "ratedLoadWeight"
        },
        {
            label: "准牵引质量(kg)",
            key: "maxTowingWeight"
        },
        {
            label: "额定载客",
            key: "ratedPassengerCount"
        },
        {
            label: "轮胎数",
            key: "tireCount"
        },
        {
            label: "轮胎规格",
            key: "tireSpecification"
        },
        {
            label: "端面宽度(mm)",
            key: "endFaceWidth"
        },
        {
            label: "外直径(mm)",
            key: "outerDiameter"
        },
        {
            label: "静半径(mm)",
            key: "staticRadius"
        },
        {
            label: "最高车速(km/h)",
            key: "maxSpeed"
        },
        {
            label: "底盘型号",
            key: "chassisModel"
        },
        {
            label: "底盘企业(公告)",
            key: "chassisManufacturerPublic"
        },
        {
            label: "底盘企业(公告)简称",
            key: "chassisManufacturerShort"
        },
        {
            label: "底盘企业(实际生产）",
            key: "chassisManufacturerActual"
        },
        {
            label: "底盘集团",
            key: "chassisGroup"
        },
        {
            label: "底盘省份",
            key: "chassisProvince"
        },
        {
            label: "底盘城市",
            key: "chassisCity"
        },
        {
            label: "底盘品牌",
            key: "chassisBrand"
        },
        {
            label: "底盘国产/进口",
            key: "chassisDomesticImport"
        },
        {
            label: "发动机型号",
            key: "engineModel"
        },
        {
            label: "发动机系列",
            key: "engineSeries"
        },
        {
            label: "发动机企业",
            key: "engineCompany"
        },
        {
            label: "发动机企业_简称",
            key: "engineCompanyShort"
        },
        {
            label: "燃料种类",
            key: "fuelType"
        },
        {
            label: "排量(ml)",
            key: "displacement"
        },
        {
            label: "排量段",
            key: "displacementSegment"
        },
        {
            label: "气缸数",
            key: "cylinderCount"
        },
        {
            label: "功率(kW)",
            key: "power"
        },
        {
            label: "排放标准",
            key: "emissionStandard"
        },
        {
            label: "变速器厂家",
            key: "transmissionManufacturer"
        },
        {
            label: "变速器型号",
            key: "transmissionModel"
        },
        {
            label: "变速器类型",
            key: "transmissionType"
        },
        {
            label: "变速器速比",
            key: "transmissionRatio"
        },
        {
            label: "主减速比",
            key: "mainReductionRatio"
        },
        {
            label: "后桥型号",
            key: "rearAxleModel"
        },
        {
            label: "后桥厂家",
            key: "rearAxleManufacturer"
        },
        {
            label: "整车辅助制动",
            key: "vehicleAuxiliaryBrake"
        },
        {
            label: "马力",
            key: "horsepower"
        },
        {
            label: "整车企业",
            key: "vehicleCompany"
        },
        {
            label: "总质量区间",
            key: "totalWeightRange"
        },
        {
            label: "车长区间",
            key: "vehicleLengthRange"
        },
        {
            label: "燃料种类_玉柴",
            key: "fuelTypeYuchai"
        },
        {
            label: "细分市场",
            key: "segmentedMarket1"
        },
        {
            label: "板块细分",
            key: "segmentDetail"
        },
        {
            label: "细分市场1",
            key: "subMarket1"
        },
        {
            label: "细分市场2",
            key: "subMarket2"
        },
        {
            label: "细分市场3",
            key: "subMarket3"
        },
        {
            label: "微改区分",
            key: "microModification"
        },
        {
            label: "车型MN类划分",
            key: "modelMnClassification"
        },
        {
            label: "车型MN类细分",
            key: "modelMnClassificationDetail"
        },
        {
            label: "销量",
            key: "salesVolume"
        },
        {
            label: "底盘型号代号",
            key: "chassisModelCode"
        },
        {
            label: "大体车型",
            key: "generalModel"
        },
        {
            label: "重中轻-按总质量",
            key: "weightClassification"
        },
        {
            label: "马力段",
            key: "horsepowerSegment"
        },
        {
            label: "驱动形式",
            key: "driveForm"
        },
        {
            label: "细分市场",
            key: "segment2"
        },
        {
            label: "细分",
            key: "subSegment"
        },
        {
            label: "发动机厂名称",
            key: "engineFactoryName"
        },
        {
            label: "发动机厂全称",
            key: "engineFactoryFullName"
        },
        {
            label: "玉柴细分",
            key: "yuchaiDetail"
        },
        {
            label: "车长米数(厘米位四舍五入)",
            key: "vehicleLengthCm"
        },
        {
            label: "总质量区间2",
            key: "totalWeightRange2"
        },
        {
            label: "排放",
            key: "emission"
        },
        {
            label: "SCB燃料类型",
            key: "scbFuelType"
        },
        {
            label: "柴气电",
            key: "dieselGasElectric"
        },
        {
            label: "新能源",
            key: "newEnergy"
        },
        {
            label: "欧系轻客",
            key: "europeanLightTruck"
        },
        {
            label: "马力段-按细分及驱动",
            key: "horsepowerSegmentDetail"
        },
        {
            label: "中重卡、3.5吨轻卡",
            key: "mediumHeavyTruck"
        },
        {
            label: "柴气其他",
            key: "dieselGasOthers"
        },
        {
            label: "细分6-按车辆名称",
            key: "subSegment6"
        },
        {
            label: "产品细分1",
            key: "productSegment1"
        },
        {
            label: "产品细分2",
            key: "productSegment2"
        },
        {
            label: "产品细分3",
            key: "productSegment3"
        },
        {
            label: "马力PS",
            key: "horsepowerPs"
        },
        {
            label: "全部底盘厂家",
            key: "allChassisManufacturers"
        },
        {
            label: "全部底盘厂家-简称",
            key: "allChassisManufacturersShor"
        },
        {
            label: "企业名称简称",
            key: "companyNameShort"
        },
        {
            label: "马力段5",
            key: "horsepowerSegment5"
        },
        {
            label: "马力段6",
            key: "horsepowerSegment6"
        },
        {
            label: "排放国6国5",
            key: "emissionStandard65"
        },
        {
            label: "排量_按发动机型号",
            key: "displacementByEngineModel"
        },
        {
            label: "排量L",
            key: "displacementL"
        },
        {
            label: "排量范围",
            key: "displacementRange"
        },
        {
            label: "排量范围2",
            key: "displacementRange2"
        },
        {
            label: "马力_按发动机型号",
            key: "horsepowerByEngineModel"
        },
        {
            label: "马力段10Ps",
            key: "horsepowerSegment10Ps"
        },
        {
            label: "马力段5Ps",
            key: "horsepowerSegment5Ps"
        },
        {
            label: "系列",
            key: "series"
        },
        {
            label: "发动机厂名称2",
            key: "engineFactoryName2"
        },
        {
            label: "燃料种类_芯蓝",
            key: "fuelTypeXinlan"
        },
        {
            label: "燃料种类_芯蓝2",
            key: "fuelTypeXinlan2"
        },
        {
            label: "动力结构1",
            key: "powerStructure1"
        },
        {
            label: "动力结构2",
            key: "powerStructure2"
        },
        {
            label: "销售省份-简",
            key: "salesProvinceShort"
        },
        {
            label: "销售城市-简",
            key: "salesCityShort"
        },
        {
            label: "新省份",
            key: "newProvince"
        },
        {
            label: "随改厂",
            key: "modifiedFactory"
        },
        {
            label: "实际车长L",
            key: "actualVehicleLength"
        },
        {
            label: "米数段",
            key: "meterSegment"
        },
        {
            label: "米数段_新",
            key: "meterSegmentNew"
        },
        {
            label: "车长米数",
            key: "vehicleLengthM"
        },
        {
            label: "米数2",
            key: "meterSegment2"
        },
        {
            label: "米数_保留",
            key: "meterKeep"
        },
        {
            label: "车重范围",
            key: "vehicleWeightRange"
        },
        {
            label: "车重范围2",
            key: "vehicleWeightRange2"
        },
        {
            label: "车重范围3",
            key: "vehicleWeightRange3"
        },
        {
            label: "专用车特征1",
            key: "specialVehicleFeature1"
        },
        {
            label: "专用车特征2",
            key: "specialVehicleFeature2"
        },
        {
            label: "新场景3级",
            key: "newScene3Level"
        },
        {
            label: "新场景2级",
            key: "newScene2Level"
        },
        {
            label: "新场景1级",
            key: "newScene1Level"
        },
        {
            label: "使用性质",
            key: "usageProperty"
        },
        {
            label: "弹簧片数",
            key: "springCount"
        },
        {
            label: "品系",
            key: "breed"
        },
        {
            label: "品系1.3",
            key: "breed13"
        },
        {
            label: "底盘实际生产企业_玉柴",
            key: "chassisActualProducerYuchai"
        },
        {
            label: "底盘实际生产企业2",
            key: "chassisActualProducer2"
        },
        {
            label: "主机厂_新",
            key: "manufacturers"
        },
        {
            label: "底盘型号对应企业简称",
            key: "chassisMCoCompanyShort"
        },
        {
            label: "细分市场维度1",
            key: "subMarketDimension1"
        },
        {
            label: "细分市场维度2",
            key: "subMarketDimension2"
        },
        {
            label: "细分市场维度3",
            key: "subMarketDimension3"
        },
        {
            label: "细分市场维度1.2",
            key: "subMarketDimension12"
        },
        {
            label: "细分市场维度2.2",
            key: "subMarketDimension22"
        },
        {
            label: "细分市场维度3.2",
            key: "subMarketDimension32"
        },
        {
            label: "细分市场维度1.3",
            key: "subMarketDimension13"
        },
        {
            label: "细分市场2",
            key: "segmentedMarket2"
        },
        {
            label: "品系2",
            key: "breed2"
        },
        {
            label: "数据来源",
            key: "dataSource"
        },
        {
            label: "重中轻",
            key: "weightMidLight"
        },
        {
            label: "细分市场维度1.4",
            key: "subMarketDimension14"
        },
        {
            label: "汽车集团",
            key: "automobileGroup"
        },
        {
            label: "星期",
            key: "week"
        }
    ])

    // 船电数
    const cds = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            dataType: 'date',
            setValue: {
                year: 'YYYY',
                month: 'M月'
            },
            table: false
        },
        {
            label: "发动机厂家",
            key: "engineManufacturers"
        },
        {
            label: "细分市场一",
            key: "marketSegment1"
        },
        {
            label: "年份",
            key: "year",
            form: false
        },
        {
            label: "月份",
            key: "month",
            form: false
        },
        {
            label: "销量",
            key: "sales"
        },
        {
            label: "板块",
            key: "sector"
        }
    ])

    // 流向数
    const lxs = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            disabled: false,
            dataType: 'date',
            setValue: {
                year: 'YYYY年'
            },
            table: false
        },
        {
            label: "来源",
            key: "source"
        },
        {
            label: "年份",
            key: "year",
            form: false
        },
        {
            label: "区域",
            key: "region"
        },
        {
            label: "省",
            key: "province"
        },
        {
            label: "市",
            key: "city"
        },
        {
            label: "经销商",
            key: "dealer"
        },
        {
            label: "车型号",
            key: "modelNumber"
        },
        {
            label: "车辆名称",
            key: "vehicleName"
        },
        {
            label: "车辆类别",
            key: "vehicleClass"
        },
        {
            label: "中文品牌",
            key: "chineseBrand"
        },
        {
            label: "车型",
            key: "model"
        },
        {
            label: "车辆级别",
            key: "vehicleGrade"
        },
        {
            label: "吨位段",
            key: "tonnageSegment"
        },
        {
            label: "发动机",
            key: "engine"
        },
        {
            label: "发动机企业",
            key: "engineManufacturer"
        },
        {
            label: "缸数",
            key: "cylinderCount"
        },
        {
            label: "缸径×行程(mm)",
            key: "cylinderBoreStrokeMm"
        },
        {
            label: "排量",
            key: "displacement"
        },
        {
            label: "马力",
            key: "horsepower"
        },
        {
            label: "燃料种类",
            key: "fuelType"
        },
        {
            label: "排放水平",
            key: "emissionLevel"
        },
        {
            label: "驾驶室",
            key: "cabType"
        },
        {
            label: "驱动",
            key: "drive"
        },
        {
            label: "轴距",
            key: "wheelbase"
        },
        {
            label: "后桥",
            key: "rearAxle"
        },
        {
            label: "主减速比",
            key: "finalDriveRatio"
        },
        {
            label: "变速箱",
            key: "transmission"
        },
        {
            label: "变速箱企业",
            key: "transmissionManufacturer"
        },
        {
            label: "底盘型号",
            key: "chassisModel"
        },
        {
            label: "底盘企业",
            key: "chassisManufacturer"
        },
        {
            label: "系列",
            key: "series"
        },
        {
            label: "排放",
            key: "emissionStandard"
        },
        {
            label: "大体车型",
            key: "generalVehicleType"
        },
        {
            label: "字头",
            key: "serialNumber"
        },
        {
            label: "总质量",
            key: "totalWeight"
        },
        {
            label: "发动机厂家全称",
            key: "engineManufacturerFull"
        },
        {
            label: "产品用途",
            key: "productUse"
        },
        {
            label: "产品轻中重",
            key: "productType"
        },
        {
            label: "产品名称1",
            key: "productName1"
        },
        {
            label: "产品名称(牵载专自)",
            key: "productNameSpecial"
        },
        {
            label: "马力段",
            key: "horsepowerRange"
        },
        {
            label: "重中轻-马力段分法",
            key: "heavyMediumLightHorsepower"
        },
        {
            label: "重中轻-马力段分法2",
            key: "heavyMediumLightHorsepwer2"
        },
        {
            label: "产品名称5",
            key: "productName5"
        },
        {
            label: "重中轻-行业分法",
            key: "heavyMediumLightIndustry1"
        },
        {
            label: "重中轻-行业分法1",
            key: "heavyMediumLightIndustry2"
        },
        {
            label: "重中轻-行业分法2",
            key: "heavyMediumLightIndustry3"
        },
        {
            label: "重中轻-行业分法3",
            key: "heavyMediumLightIndustry4"
        },
        {
            label: "重中轻(牵载自)-行业分法4",
            key: "heavyMediumLightIndustry5"
        },
        {
            label: "1月",
            key: "janSales"
        },
        {
            label: "2月",
            key: "febSales"
        },
        {
            label: "3月",
            key: "marSales"
        },
        {
            label: "4月",
            key: "aprSales"
        },
        {
            label: "5月",
            key: "maySales"
        },
        {
            label: "6月",
            key: "junSales"
        },
        {
            label: "7月",
            key: "julSales"
        },
        {
            label: "8月",
            key: "augSales"
        },
        {
            label: "9月",
            key: "sepSales"
        },
        {
            label: "10月",
            key: "octSales"
        },
        {
            label: "11月",
            key: "novSales"
        },
        {
            label: "12月",
            key: "decSales"
        },
        {
            label: "厂家全年",
            key: "annualSales"
        },
        {
            label: "厂家当月累计",
            key: "monthlyCumulative"
        },
        {
            label: "玉柴全年",
            key: "yuchaiAnnualSales"
        },
        {
            label: "玉柴当月累计",
            key: "yuchaiMonthlyCumulative"
        },
        {
            label: "厂家名称统一",
            key: "manufacturerName"
        },
        {
            label: "发动机厂家简称",
            key: "engineManufacturerS"
        }
    ])


    // 友商数
    const yss = ref([
        {
            label: '日期范围',
            key: 'dataRange',
            dataType: 'date',
            setValue: {
                year: 'YYYY年',
                month: 'M月'
            },
            table: false
        },
        {
            label: "厂家",
            key: "manufacturer"
        },
        {
            label: "年度",
            key: "year",
            form: false
        },
        {
            label: "月度",
            key: "month",
            form: false
        },
        {
            label: "季度",
            key: "quarter"
        },
        {
            label: "机型",
            key: "engineModel"
        },
        {
            label: "机型1",
            key: "engineModel1"
        },
        {
            label: "排量",
            key: "displacement"
        },
        {
            label: "排量段",
            key: "displacementRange"
        },
        {
            label: "生产工厂1",
            key: "productionPlant1"
        },
        {
            label: "生产厂(按生产表)",
            key: "productionPlantActual"
        },
        {
            label: "细分市场",
            key: "marketSegment"
        },
        {
            label: "板块2",
            key: "sector2"
        },
        {
            label: "板块1",
            key: "sector1"
        },
        {
            label: "销量",
            key: "salesVolume"
        },
        {
            label: "功率",
            key: "power"
        },
        {
            label: "物料号",
            key: "materialNumber"
        },
        {
            label: "状态机",
            key: "statusMachine"
        },
        {
            label: "销售结构1",
            key: "salesStructure1"
        },
        {
            label: "排量范围1",
            key: "displacementRange1"
        },
        {
            label: "功率范围-船电",
            key: "powerRangeShipElectric"
        },
        {
            label: "排量范围-四段",
            key: "displacementRange4Segments"
        },
        {
            label: "公司",
            key: "company"
        },
        {
            label: "排量范围-四段3.8L",
            key: "displacementRange38L"
        },
        {
            label: "排量范围-升段",
            key: "displacementRangeUp"
        },
        {
            label: "排量范围-四段3.7L",
            key: "displacementRange4Segments37L"
        },
        {
            label: "排量范围-四段3.7L及机组",
            key: "displacementRange37LUnits"
        },
        {
            label: "实销/预测",
            key: "actualSalesForecast"
        },
        {
            label: "出口标识",
            key: "exportIndicator"
        },
        {
            label: "出口手工分",
            key: "manualExportSplit"
        },
        {
            label: "柴汽",
            key: "fuelType"
        },
        {
            label: "缸数2",
            key: "cylinderCount2"
        }
    ])
    return {znx, hgs, zjs, sxs, hyxz, cds, lxs, yss}
}
