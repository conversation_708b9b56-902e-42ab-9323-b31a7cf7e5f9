# 业务数据表格组件优化说明

## 优化前的问题

原来的表格组件（tableA.vue 到 tableG.vue）存在大量重复代码：

1. **模板结构重复**：所有组件都有相同的表格、分页、上传、下载等结构
2. **脚本逻辑重复**：相同的导入、hooks使用、初始化逻辑
3. **样式重复**：完全相同的样式定义
4. **维护困难**：修改一个功能需要在7个文件中重复修改

## 优化方案

### 1. 创建通用基础组件

**BaseBusinessTable.vue** - 通用的业务表格组件
- 接收配置参数作为props
- 包含所有重复的模板结构和逻辑
- 通过配置动态设置不同的API、URL等

### 2. 集中配置管理

**tableConfigs.js** - 表格配置文件
- 集中管理所有表格的配置信息
- 包括API函数、下载配置、上传URL、表单数据key等
- 提供配置获取的工具函数

### 3. 简化现有组件

将原来的表格组件简化为只传入配置的wrapper：

```vue
<template>
  <BaseBusinessTable :config="tableConfig" />
</template>

<script setup>
import BaseBusinessTable from './BaseBusinessTable.vue'
import { getTableConfig } from './tableConfigs.js'

const tableConfig = getTableConfig('tableA')
</script>
```

## 配置结构

每个表格的配置包含以下字段：

```javascript
{
  api: Function,           // API调用函数
  download: {              // 下载配置
    url: String,           // 下载URL
    name: String           // 文件名前缀
  },
  uploadUrls: {            // 上传URL配置
    increment: String,     // 增量导入URL
    full: String          // 全量导入URL
  },
  formDataKey: String,     // 表单数据在useFormKeyHooks中的key
  title: String,           // 表格标题
  module: String           // 模块参数
}
```

## 优化效果

1. **代码减少**：每个表格组件从100+行减少到12行
2. **维护性提升**：修改通用逻辑只需修改BaseBusinessTable.vue
3. **配置集中**：所有表格配置在一个文件中管理
4. **类型安全**：配置验证确保必要参数存在
5. **扩展性好**：新增表格只需添加配置即可

## 表格映射

| 组件 | 标题 | API | 表单数据Key |
|------|------|-----|-------------|
| tableA | 中内协 | cnEnginOrgList | znx |
| tableB | 海关数 | customsList | hgs |
| tableC | 装机数 | installList | zjs |
| tableD | 上险数 | insureList | sxs |
| tableE | 货运新增 | corNewFreightList | hyxz |
| tableF | 船电数 | shipList | cds |
| tableG | 流向数 | flowDataList | lxs |

## 使用方法

### 添加新表格

1. 在 `tableConfigs.js` 中添加新的配置
2. 创建新的表格组件文件，使用BaseBusinessTable
3. 在 `allFormKeyHooks.js` 中添加对应的表单数据

### 修改现有表格

1. 修改 `tableConfigs.js` 中对应的配置
2. 如需修改通用逻辑，编辑 `BaseBusinessTable.vue`

## 向后兼容

优化后的组件保持了与原组件相同的对外接口，不会影响父组件的使用。
