<template>
  <el-dialog v-model="visible" title="删除数据" width="500px" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker
          v-model="form.dateRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          format="YYYY年MM月"
          value-format="YYYY-MM"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="danger" :loading="loading" @click="handleConfirm"> 确认删除 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 数据库管理-行业数据-表格配置
import { serverDataDelete } from '@/api/database/serviceData'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formDataKey: {
    type: String,
    required: true
  },
  tableName: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(false)
const loading = ref(false)
const formRef = ref(null)

// formDataKey到tableKey的映射
const getTableKey = (formDataKey) => {
  const keyMap = {
    'znx': 'tableA',
    'hgs': 'tableB',
    'zjs': 'tableC',
    'sxs': 'tableD',
    'hyxz': 'tableE',
    'cds': 'tableF',
    'lxs': 'tableG'
  }
  return keyMap[formDataKey] || formDataKey
}

// 表单数据
const form = reactive({
  dateRange: []
})

// 表单验证规则
const rules = {
  dateRange: [{ required: true, message: '请选择日期范围', trigger: 'change' }]
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  val => {
    visible.value = val
    if (val) {
      // 重置表单
      form.dateRange = []
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }
  }
)

// 监听visible变化
watch(visible, val => {
  emit('update:modelValue', val)
})

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 确认删除
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    // 表单验证
    await formRef.value.validate()

    // 二次确认
    await ElMessageBox.confirm(
      `确定要删除${props.tableName}在${form.dateRange[0]}至${form.dateRange[1]}期间的数据吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    loading.value = true

    // 解析日期范围
    const [startDate, endDate] = form.dateRange
    const [startYear, startMonth] = startDate.split('-')
    const [endYear, endMonth] = endDate.split('-')
    // 调用删除接口

    const result = await serverDataDelete({
      tableKey: getTableKey(props.formDataKey),
      startYear: parseInt(startYear),
      endYear: parseInt(endYear),
      startMonth: parseInt(startMonth),
      endMonth: parseInt(endMonth)
    })

    if (result.code === 200) {
      ElMessage.success('删除成功')
      emit('success')
      handleClose()
    } else {
      throw new Error(result.message || '删除失败')
    }
  } catch (error) {
    if (error.message !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
