# 删除功能映射测试

## formDataKey到tableKey的映射关系

现在删除功能使用formDataKey作为参数，内部会自动映射到正确的tableKey：

### 映射表

| formDataKey | tableKey | 表格名称 | 说明 |
|-------------|----------|----------|------|
| znx         | tableA   | 中内协   | 中内协数据表 |
| hgs         | tableB   | 海关数   | 海关数据表 |
| zjs         | tableC   | 装机数   | 装机数据表 |
| sxs         | tableD   | 上险数   | 上险数据表 |
| hyxz        | tableE   | 货运新增 | 货运新增数据表 |
| cds         | tableF   | 船电数   | 船电数据表 |
| lxs         | tableG   | 流向数   | 流向数据表 |

### 映射逻辑

```javascript
const getTableKey = (formDataKey) => {
  const keyMap = {
    'znx': 'tableA',
    'hgs': 'tableB', 
    'zjs': 'tableC',
    'sxs': 'tableD',
    'hyxz': 'tableE',
    'cds': 'tableF',
    'lxs': 'tableG'
  }
  return keyMap[formDataKey] || formDataKey
}
```

### 使用示例

```vue
<template>
  <!-- 中内协表格 -->
  <BaseBusinessTable :config="tableAConfig" />
</template>

<script setup>
import { getTableConfig } from './tableConfigs.js'

// 获取中内协配置，formDataKey为'znx'
const tableAConfig = getTableConfig('tableA')

// 当调用删除功能时：
// 1. BaseBusinessTable传递 formDataKey='znx' 给deleteDialog
// 2. deleteDialog内部映射 'znx' -> 'tableA'
// 3. 发送给后端的参数中 tableKey='tableA'
</script>
```

### 接口调用参数

当用户在中内协表格中删除2024年1-12月的数据时：

```javascript
// 发送到后端的参数
{
  tableKey: 'tableA',    // 由'znx'映射而来
  startYear: 2024,
  endYear: 2024,
  startMonth: 1,
  endMonth: 12
}
```

### 测试步骤

1. **打开任意表格页面**
2. **触发删除功能**
3. **选择日期范围**（如：2024年1月 至 2024年12月）
4. **点击确认删除**
5. **检查网络请求**：
   - URL: `/intelligence/service-data/deleteData`
   - Method: POST
   - Body: 包含正确的tableKey映射

### 验证要点

1. **映射正确性**：确保formDataKey正确映射到tableKey
2. **参数完整性**：确保所有必需参数都正确传递
3. **错误处理**：测试各种错误情况的处理
4. **用户体验**：确保操作流程顺畅

### 调试信息

如需调试映射过程，可以在deleteDialog组件中添加console.log：

```javascript
const handleConfirm = async () => {
  // 调试信息
  console.log('formDataKey:', props.formDataKey)
  console.log('mapped tableKey:', getTableKey(props.formDataKey))
  
  // ... 其他代码
}
```

### 注意事项

1. **向后兼容**：如果formDataKey不在映射表中，会直接使用原值
2. **大小写敏感**：映射key区分大小写
3. **扩展性**：新增表格时需要同时更新映射表
