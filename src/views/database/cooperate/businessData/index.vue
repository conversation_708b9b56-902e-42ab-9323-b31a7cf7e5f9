<template>
  <div class="wrap">
    <el-tabs v-model="activeName" type="border-card" class="bi-tabs">
      <el-tab-pane
        v-if="proxy.hasPermission(['database:cooperateIndustry:znx'], true)"
        class="custom-scrollbar"
        label="中内协"
        name="中内协"
        lazy
      >
        <tableA />
      </el-tab-pane>
      <el-tab-pane
        v-if="proxy.hasPermission(['database:cooperateIndustry:hgs'], true)"
        class="custom-scrollbar"
        label="海关数"
        name="海关数"
        lazy
      >
        <tableB />
      </el-tab-pane>
      <el-tab-pane
        v-if="proxy.hasPermission(['database:cooperateIndustry:zjs'], true)"
        class="custom-scrollbar"
        label="装机数"
        name="装机数"
        lazy
      >
        <tableC />
      </el-tab-pane>
      <el-tab-pane
        v-if="proxy.hasPermission(['database:cooperateIndustry:sxs'], true)"
        class="custom-scrollbar"
        label="上险数"
        name="上险数"
        lazy
      >
        <tableD />
      </el-tab-pane>
      <el-tab-pane
        v-if="proxy.hasPermission(['database:cooperateIndustry:hyxzs'], true)"
        class="custom-scrollbar"
        label="货运新增"
        name="货运新增"
        lazy
      >
        <tableE />
      </el-tab-pane>
      <el-tab-pane
        v-if="proxy.hasPermission(['database:cooperateIndustry:cds'], true)"
        class="custom-scrollbar"
        label="船电数"
        name="船电数"
        lazy
      >
        <tableF />
      </el-tab-pane>
      <el-tab-pane
        v-if="proxy.hasPermission(['database:cooperateIndustry:lxs'], true)"
        class="custom-scrollbar"
        label="流向数"
        name="流向数"
        lazy
      >
        <tableG />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import tableA from './components/tableA.vue'
import tableB from './components/tableB.vue'
import tableC from './components/tableC.vue'
import tableD from './components/tableD.vue'
import tableE from './components/tableE.vue'
import tableF from './components/tableF.vue'
import tableG from './components/tableG.vue'
const { proxy } = getCurrentInstance()

const activeName = ref('中内协')
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.wrap {
  padding: $bi-layout-margin;
  height: $bi-main-height;
  :deep(.el-tabs) {
    border-radius: 8px;
    height: 100%;
    background: linear-gradient(0deg, #d2e6fc 0%, rgba(210, 230, 252, 0.5) 100%);

    .el-tab-pane {
      height: 100%;
      overflow: auto;
    }
  }
  :deep(.el-tabs--border-card) {
    & > .el-tabs__content {
      padding: 0 16px;
    }
  }
  :deep(.el-tabs__header) {
    margin: 0;
  }
}
</style>
