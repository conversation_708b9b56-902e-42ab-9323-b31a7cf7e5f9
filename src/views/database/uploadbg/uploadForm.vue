<template>
  <!-- 详情对话框 -->
  <el-dialog :title="title" v-model="visible" append-to-body v-if="visible" width="92%">
    <el-scrollbar height="600px">
        <el-form
          ref="configRef"
          :model="data.queryParams"
          :rules="data.rules"
          :disabled="dailogstatus === 'query'"
          label-width="100px"
        >
          <linkage :dicts="data.dicts" :form="data.queryParams" :xs="24" :sm="24" :md="24" />
          <el-row>

          <el-col :md="12" :sm="24" >
            <el-form-item label="关键字" prop="keyWord" style="width: 100%">
              <el-input v-model="data.queryParams.keyWord" placeholder="关键字" clearable />
            </el-form-item>
          </el-col>
            <el-col :md="12" :sm="24" >
            <el-form-item label="是否机密" style="width: 100%">
              <el-switch
                v-model="data.queryParams.isConfidential"
                :disabled="dailogstatus === 'query'"
                :active-value="1"
                :inactive-value="0"
                active-text="机密"
                inactive-text="非机密"
                active-color="#ff4949"
                inactive-color="#13ce66"
              />
            </el-form-item>
          </el-col>
         
      </el-row>

      <el-row :gutter="20">
        <el-col :md="12" :sm="24" >
            <el-form-item label="是否可查看" style="width: 100%">
              <el-switch
                v-model="data.queryParams.canView"
                :disabled="dailogstatus === 'query'"
                :active-value="1"
                :inactive-value="0"
                active-text="可查看"
                inactive-text="不可查看"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
          </el-col>
         <el-col :md="12" :sm="24"  v-if="data.queryParams.isConfidential === 0 || (data.queryParams.isConfidential === 1 && data.queryParams.canView === 1)">
            <el-form-item label="能否下载" style="width: 100%">
              <el-switch
                v-model="data.queryParams.canDownload"
                :disabled="dailogstatus === 'query'"
                :active-value="1"
                :inactive-value="0"
                active-text="可以下载"
                inactive-text="不能下载"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
          </el-col>
          
      </el-row>

          <el-form-item label="报告" prop="" style="width: 100%">
            <FileUpload @form_data="handleForm" :fileList="data.fileList" />
          </el-form-item>
          <!-- data.queryParams.canDownload === 1 -->
          <el-form-item label="阅读权限" style="width: 100%" v-if="data.queryParams.canDownload === 1 && (data.queryParams.isConfidential === 0 ||  (data.queryParams.isConfidential === 1 && data.queryParams.canView === 1))">
            <TableTransfer
              v-if="visible"
              v-model="selectedUsers"
              :search-config="searchConfig"
              :display-config="displayConfig"
              :api-config="apiConfig"
              :record-id="data.queryParams.id"
              :in-dialog="true"
              @change="handleChange"
            />
            <!-- 分页信息提示 -->
            <!-- <div class="pagination-info" v-if="userPagination.total > 0">
          <span>已加载 {{ allUsers.length }} / {{ userPagination.total }} 条数据</span>
          <span v-if="userPagination.hasMore" style="color: #409eff; margin-left: 10px;">
            滚动到底部加载更多
          </span>
          <span v-else style="color: #909399; margin-left: 10px;">
            已加载全部数据
          </span>
        </div> -->
          </el-form-item>
        </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-if="dailogstatus !== 'query'" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import linkage from '@/views/components/linkage.vue'
import FileUpload from './FileUpload/index.vue'
import TableTransfer from '@/components/TableTransfer/index.vue'
import * as upload from '@/api/database/upload'
import {
  selectNotFileUser,
  selectFileUser,
  setFileUser,
  deleteFileUser
} from '@/api/database/upload'
import { allListRole } from '@/api/system/role'

const emit = defineEmits()
const { proxy } = getCurrentInstance()
const store = useStore()
const modelValue1 = ref({})

const visible = ref(false)
const title = ref('')
const dailogstatus = ref('')
const loading = ref(false)
const input = ref('')
// 穿梭框相关变量
const selectedUsers = ref([])

// 搜索配置
const searchConfig = ref({
  nameField: 'name',
  namePlaceholder: '按姓名搜索',
  roleField: 'role',
  rolePlaceholder: '按角色搜索',
  roleOptions: []
})

// 显示字段配置
const displayConfig = ref({
  nameField: 'name',
  departmentField: 'department',
  roleField: 'role',
  keyField: 'id'
})

// API 配置
const apiConfig = ref({
  loadUsers: selectNotFileUser,
  loadSelectedUsers: selectFileUser,
  loadRoles: allListRole,
  addSelectedUsers: setFileUser,
  removeSelectedUsers: deleteFileUser
})
const data = reactive({
  dicts: [],
  queryParams: {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '02',
    userIdList: [],
    canDownload: 0,
    isConfidential: 0,
    canView: 1
  },
  fileList: [],
  rules: {
    dictModuleId: [{ required: true, message: '信息模块不能为空', trigger: 'blur' }],
    dictLeftMenuId: [
      { required: true, message: '左侧菜单不能为空，请先选择信息模块', trigger: 'blur' }
    ],
    dictNewsId: [
      { required: true, message: '新闻种类不能为空，请先选择左侧菜单', trigger: 'blur' }
    ],
    keyWord: [{ required: true, message: '关键字不能为空', trigger: 'blur' }]
    // userIdList: [{ required: true, message: '请选择人员', trigger: 'change' }]
  }
})
// 简化的初始化数据函数（现在主要由 TableTransfer 组件处理）
function getData() {
  // 这个函数现在主要用于兼容性，实际数据加载由 TableTransfer 组件处理
  return Promise.resolve()
}

store.dispatch('getNewsLinkageData').then(res => {
  data.dicts = res
})

// 初始化穿梭框数据
getData()

/** 详情按钮操作 */
function show(params, row) {
  visible.value = true
  title.value = '上传报告'

  dailogstatus.value = params
  if (params !== 'add') {
    const queryParams = JSON.parse(JSON.stringify(row))
    data.queryParams = queryParams

    // { fileKey: res.data.name, url: res.data.url, fileName: file.name }

    data.fileList = [
      { name: queryParams.fileKey, url: queryParams.url ?? '', fileName: queryParams.fileName }
    ]
    modelValue1.value = [
      { name: queryParams.fileKey, url: queryParams.url ?? '', fileName: queryParams.fileName }
    ]
    // console.log('参数内容', queryParams)
    // console.log('已选用户', queryParams.userIdList && queryParams.userIdList.length > 0)
    // 编辑模式下，TableTransfer 组件会根据 record-id 自动加载数据
  } else {
    data.fileList = []
    modelValue1.value = []
    resetForm()
    input.value = ''
    // 新增模式下，TableTransfer 组件会自动初始化数据
  }
}
/** 取消按钮 */
function cancel() {
  visible.value = false
}
function resetForm() {
  data.queryParams = {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '02',
    userIdList: [],
    canDownload: 0,
    isConfidential: 0,
    canView: 1
  }
  // 重置穿梭框数据
  selectedUsers.value = []
}
function handleForm(val) {
  modelValue1.value = val
}
/** 提交按钮 */
function submitForm() {
  if (Object.keys(modelValue1.value).length === 0) {
    proxy.$modal.msgError('必须上传报告!')
    return
  }

  proxy.$refs['configRef'].validate(valid => {
    if (valid) {
      loading.value = true
      upload
        .addupdate({ ...data.queryParams, ...modelValue1.value }, dailogstatus.value)
        .then(response => {
          loading.value = false
          proxy.$modal.msgSuccess(response.msg)
          visible.value = false
          emit('ok')
        })
        .catch(() => {
          loading.value = false
          proxy.$modal.msgError(response.msg)
        })
      // console.log(selectedUsers.value.map(x=>x.id))
      // let userIdList = selectedUsers.value.map(x=>x.id);
      // { id:props.row.id, userIdList }
      // setFileUser({ id:data.queryParams.id, userIdList })
    }
  })
}
// 穿梭框数据变化事件
const handleChange = value => {
  // console.log('穿梭框数据变化:', value)
  // 更新表单中的用户ID列表
  data.queryParams.userIdList = value.map(user => user.id)
  selectedUsers.value = value
}

// 这些事件处理函数已经移动到 TableTransfer 组件内部

defineExpose({
  show
})
</script>

<style scoped>
.pagination-info {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  text-align: center;
}
</style>
