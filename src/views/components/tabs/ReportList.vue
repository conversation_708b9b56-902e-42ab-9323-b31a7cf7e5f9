<!-- 报告区组件 -->
<template>
  <div class="content">
    <el-form label-width="0" :inline="true" class="tabs-form">
      <el-row :gutter="16" style="margin-right: unset">
        <el-col :xs="16" :sm="16" :md="6">
          <el-date-picker
            v-model="data.params.dataRange"
            type="daterange"
            range-separator="-"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%"
            clearable
            :disabledDate="disabledDate"
          />
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-input
            v-model="data.params.keyWord"
            :suffix-icon="Search"
            placeholder="请输入关键字"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-button type="primary" color="#0B5FC5" @click="handleQuery" style="width: 60px">
            搜索
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table class="table-box" :data="data.list">
      <el-table-column label="文件名">
        <template #default="scope">
          <span :title="scope.row.fileKey">{{ scope.row.fileName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="keyWord" label="关键字" />
      <el-table-column prop="createTime" width="150" label="日期" />
      <el-table-column width="140" label="操作">
        <template #default="{ row }">
       
          <el-button-group style="margin-left: -12px">
            <el-button type="primary" @click="downloadWorld(row)" text v-if="row.canDownload == '1' "> 下载 </el-button>
            <el-button type="primary" @click="previewResource(row)" text v-if="row.canView == '1'"> 预览 </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-box">
      <BiPagination
        :total="data.total"
        v-model:page="data.params.pageNum"
        v-model:limit="data.params.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import BiPagination from '@/views/components/BiPagination.vue'
import { Base64 } from 'js-base64'
import { disabledDate } from './config.js'
import { getReportList, getReportListByAuth } from '@/api/common/common.js'
import { signedUrl } from '@/api/upload.js'
const store = useStore()
const getters = computed(() => store.getters)

const { proxy } = getCurrentInstance()
const route = useRoute()
const date = new Date()
const year = date.getFullYear()
const month = date.getMonth() + 1
const day = date.getDate()
const currentDay = `${year}-${month}-${day}`
const prevDay = `${year - 1}-${month}-${day}`
const data = reactive({
  params: {
    dictModuleId: '',
    dictLeftMenuId: '',
    dictNewsId: route?.meta?.menuId,
    fileType: '02',
    s_date: '',
    e_date: '',
    pageNum: 1,
    pageSize: 15,
    fileName: '',
    dataRange: [prevDay, currentDay] // 前端展示用
  },
  list: [],
  total: 0
})
const loading = reactive({
  table: false
})

/** 查询列表 */
const getList = async () => {
  if (loading.table) return
  loading.table = true
  if (data.params.dataRange && data.params.dataRange > 0) {
    data.params.s_date = data.params.dataRange[0]
    data.params.e_date = data.params.dataRange[1]
  } else {
    data.params.s_date = ''
    data.params.e_date = ''
  }
  const res = await getReportListByAuth(data.params)
  loading.table = false
  if (res.code !== 200) {
    proxy.$modal.msgError(res.msg)
    return
  }
  data.list = res.rows
  data.total = res.total
}

/** 搜索按钮操作 */
function handleQuery() {
  data.params.pageNum = 1
  getList()
}

// 搜索区域
const queryParams = reactive({
  dictModuleId: '',
  dictLeftMenuId: '',
  dictNewsId: route?.meta?.menuId,
  fileType: '02',
  dataRange: [], // 前端用
  s_date: '',
  e_date: '',
  pageNum: 1,
  pageSize: 15,
  fileName: ''
})
let dataRange = ref([])
// 点击查询
const onSearch = () => {
  console.log('dataRange', dataRange.value)

  if (dataRange.value && dataRange.value.length > 0) {
    queryParams.s_date = dataRange?.value[0]
    queryParams.e_date = dataRange?.value[1]
  } else {
    queryParams.s_date = ''
    queryParams.e_date = ''
  }

  getReportListFun()
}

const downloadWorld = row => {
  console.log(row.fileName)
  proxy.downloadJSON(
    `/upload/download?fileName=${encodeURI(row.fileName)}&fileKey=${encodeURI(row.fileKey)}`,
    {},
    `${row.fileName}`
  )
}

const previewResource = async ({ fileKey }) => {
  // signedUrl({ fileKey: encodeURI(fileKey) }).then(res => {
  //   window.open(res.data, '_blank')
  // })

  // 判断当前是否为开发环境
  const isDev = import.meta.env.DEV
  // 根据环境设置域名：开发环境使用测试域名，生产环境使用当前域名
  const domain = isDev ? 'http://iis-spider.qas.yuchai.com' : window.location.origin
  // 构建预览URL基础部分
  let url = domain + '/apaasFile/onlinePreview?url='
  // 获取附件详细信息
  let { code, data } = await signedUrl({ fileKey: encodeURI(fileKey) })
  console.log('code', data)
  if (code !== 200) return
  // 解析附件路径URL
  const urlX = new URL(data)
  // 对Signature参数进行URL编码
  urlX.searchParams.set('Signature', encodeURIComponent(urlX.searchParams.get('Signature')))
  // 生成水印文本（用户名+登录日期）并进行URL编码
  let watermarkTxt = encodeURIComponent(`${getters.value.name}${getters.value.loginDate}`)
  // 构建完整预览URL：基础URL + 编码后的附件路径 + 水印参数
  url = url + encodeURIComponent(Base64.encode(urlX.toString())) + '&watermarkTxt=' + watermarkTxt
  // 在新窗口打开预览
  window.open(url, '_blank')
}

getList()
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .table-box {
    margin-top: 10px;
    flex: 1;
  }
}
</style>
