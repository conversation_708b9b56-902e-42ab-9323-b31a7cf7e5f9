<template>
  <div class="chartTips" style="padding: 8px 4px" :style="{ maxWidth: width }">
    <div style="margin-bottom: 5px" class="tipTitle">
      <span name="title" :title="params && params[0] ? params[0].axisValue : ''">
        {{
          params && params[0] ? params[0].axisValue : params.axisValue
        }}
      </span>
      <slot name="hander-right" :params="params"></slot>
    </div>
    <!-- 修改 el-row，添加响应式属性 -->
    <el-row :gutter="20" :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
      <template v-if="singleColumn">
        <TooltipItemColumn :items="leftParams" prefix="left" :params="params" :showLineValues="showLineValues">
          <template #item="{ item }">
            <slot name="item" :item="item"></slot>
          </template>
        </TooltipItemColumn>
      </template>
      <template v-else>
        <TooltipItemColumn :items="leftParams" prefix="left" :params="params" :showLineValues="showLineValues">
          <template #item="{ item }">
            <slot name="item" :item="item" />
          </template>
        </TooltipItemColumn>
        <TooltipItemColumn :items="rightParams" prefix="right" :params="params" :showLineValues="showLineValues">
          <template #item="{ item }">
            <slot name="item" :item="item" />
          </template>
        </TooltipItemColumn>
      </template>
    </el-row>
    <div v-if="showTotal" name="total" class="tipTotal">
      <slot name="total" :params="params">
        <div>
          <span style="
              display: inline-block;
              margin-right: 4px;
              border-radius: 10px;
              width: 10px;
              height: 10px;
              background-color: #115e93;
            ">
          </span>
          总计:
        </div>
        <slot name="total-num" :params="params">
          <div>{{numberFormat(parseInt(params?.reduce((sum, x) => sum + x.value, 0)), 0) || 0}}台</div>
        </slot>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ElRow } from 'element-plus'
import { computed } from 'vue'
import TooltipItemColumn from './TooltipItemColumn.vue'
import { numberFormat } from '../../../utils/format'
defineOptions({
  name: 'tooltip'
})

/**
 * 组件的 props 配置
 */
const props = defineProps({
  /**
   * 提示框的宽度
   * @type {String}
   * @default '50vw'
   */
  width: {
    type: String,
    default: '50vw'
  },
  /**
   * 提示框所需的数据数组
   * @type {Array}
   * @default []
   */
  params: {
    type: Array,
    default: () => []
  },
  /**
   * 提示框的标题
   * @type {String}
   * @default ''
   */
  title: {
    type: String,
    default: ''
  },
  /**
   * 是否显示总数信息
   * @type {Boolean}
   * @default false
   */
  showTotal: {
    type: Boolean,
    required: false,
    default: false
  },
  /**
   * 数据映射对象，用于转换数据
   * @type {Object}
   * @default undefined
   */
  mapping: {
    type: Object,
    required: false,
    default: {
      sales: 'sales',
      proportion: 'slice',
      yoy: 'slice'
    }
  },
  /**
   * 是否需要对数据进行排序
   * @type {Boolean}
   * @default true
   */
  shouldSort: {
    type: Boolean,
    required: false,
    default: true
  },
  /**
   * 指定排序字段
   * @type {String}
   * @default 'value'
   */
  sortField: {
    type: String,
    default: 'proportion'
  },
  /**
  * 是否置顶同比
  * @type {Boolean}
  * @default false
  */
  isYoySortField: {
    type: Boolean,
    default: false
  },
  /**
   * 是否使用单列布局
   * @type {Boolean}
   * @default false
   */
  singleColumn: {
    type: Boolean,
    default: true
  },
  /**
   * 需要特殊处理并移到最后的关键词数组
   * @type {Array}
   * @default ['其他','其它']
   */
  specialKeywords: {
    type: Array,
    default: () => ['其他', '其它']
  },
  /**
   * 是否显示线条类型(line)的数值
   * @type {Boolean}
   * @default true
   */
  showLineValues: {
    type: Boolean,
    default: true
  }
})

// 添加转换函数
const convertParams = (item, mapping) => {
  const converted = { ...item }
  converted.data = {
    sales: item.data[mapping.sales] || 0,
    proportion: item.data[mapping.proportion] || 0,
    yoy: item.data[mapping.yoy] || 0,
    value: item.data[props.sortField] || 0
  }
  return converted
}

const sortedParams = computed(() => {
  // props.params 是对象就直接返回
  if (!Array.isArray(props.params)) {
    return props.params;
  }
  if (!props.shouldSort) {
    return [...props.params]
      .filter(item => !item.seriesName?.includes('总计'))
      .map(item => convertParams(item, props.mapping))
  }
  // 先进行常规排序
  let processedItems = [...props?.params]
    .filter(item => !item.seriesName?.includes('总计'))
    .map(item => convertParams(item, props.mapping))
    .sort((a, b) => {
      const isA_Tongbi = a.seriesName?.includes('同比') || false
      const isB_Tongbi = b.seriesName?.includes('同比') || false
      // 处理'同比'项
      if (isA_Tongbi && !isB_Tongbi) {
        return props.isYoySortField ? -1 : 1;
      }
      if (!isA_Tongbi && isB_Tongbi) {
        return props.isYoySortField ? -1 : 1;
      }
      // 常规排序
      const valueA = a.data?.[props.sortField] || 0
      const valueB = b.data?.[props.sortField] || 0
      return valueB - valueA
    })

  // 将特殊关键词项移到最后
  let mainItems = [...processedItems];
  let specialItems = [];

  props.specialKeywords.forEach(keyword => {
    const itemsToMove = mainItems.filter(item => item.seriesName?.includes(keyword));
    mainItems = mainItems.filter(item => !item.seriesName?.includes(keyword));
    specialItems = specialItems.concat(itemsToMove);
  });

  const sorted = mainItems.concat(specialItems);

  return sorted
})

// 将排序后的数组拆分为左右两部分
// 修改左右列的计算逻辑
const leftParams = computed(() => {
  if (props.singleColumn) {
    return sortedParams.value
  }
  const halfLength = Math.ceil(sortedParams.value.length / 2)
  return sortedParams.value.slice(0, halfLength)
})

const rightParams = computed(() => {
  if (props.singleColumn) {
    return []
  }
  const halfLength = Math.ceil(sortedParams.value.length / 2)
  return sortedParams.value.slice(halfLength)
})
</script>

<style scoped>
/* 添加或修改以下样式，并使用 !important 提升优先级 */
.el-row {
  display: flex !important;
  flex-wrap: nowrap !important;
  margin-bottom: 0px !important;
}

.el-col {
  flex: 1 !important;
  min-width: 0 !important;
  /* 防止内容溢出导致换行 */
}

.tipItemRight {
  display: flex;
  gap: 10px;
}

.tipTitle {
  display: flex;
  justify-content: space-between;
}

.tipTotal {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}
</style>
