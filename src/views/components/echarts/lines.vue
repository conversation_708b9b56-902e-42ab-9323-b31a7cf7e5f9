<template>
  <el-card>
    <template #header>
      <block-title :title="props.title" :icon="props.titleIcon" />
    </template>
    <div class="ratio-width" :style="{ paddingBottom: props.height }">
      <div ref="target" class="ratio-width__wrap" />
      <el-tooltip
        v-if="contentTip"
        :content="contentTip"
        popper-class="bar-box-tooltip"
        placement="top"
      >
        <el-icon :size="18" color="#00A9F4" class="bar-box__warn"><InfoFilled /></el-icon>
      </el-tooltip>
    </div>
  </el-card>
</template>

<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import * as echarts from 'echarts'
import echartsResize from '@/utils/hooks/echartsResize.js'
import { color, colorMap, tooltip, formatter, titleTipObj } from './config.js'
import { sortLegendWithOtherFirst } from '@/utils/common/method.js'
const props = defineProps({
  title: {
    // 是否展示标题
    type: String,
    required: false,
    default: ''
  },
  titleIcon: {
    // 是否展示标题
    type: String,
    required: false,
    default: 'data1'
  },
  titleRotate: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  series: {
    // series数据
    type: Array,
    required: true,
    default: () => []
  },
  color: {
    // 每条折线的颜色
    type: Array,
    required: false,
    default: () => color
  },
  grid: {
    // grid数据
    type: Object,
    required: false,
    default: () => ({ left: 56, bottom: 46, right: 46, top: 46 })
  },
  height: {
    // 是否展示标题
    type: String,
    required: false,
    default: '60%'
  },
  yAxisName: {
    type: String,
    required: false,
    default: ''
  },
  yAxisLabelFormate: {
    type: String,
    required: false,
    default: ''
  },
  tooltipUnits: {
    type: String,
    required: false,
    default: ''
  },
  precision: {
    type: Number,
    required: false,
    default: 1
  },
  reverseLegend: {
    type: Boolean,
    required: false,
    default: true
  },
  legend: {
    type: Object,
    required: false,
    default: () => ({})
  }
})
const titleIcon = reactive({
  a: new URL('@/assets/images/title-icon.png', import.meta.url).href
})
watch(
  () => props.series,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 初始化实例
let myChart = null
const target = ref(null)
const contentTip = ref('')

onMounted(() => {
  myChart = echarts.init(target.value)
  renderEcharts()
  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)

  myChart.on('click', params => {
    if (params.seriesType === 'line') {
      const series = props.series.map((item, index) => ({
        label: {
          show: index === params.seriesIndex,
          position: 'top',
          formatter: '{c}万台'
        }
      }))
      myChart.setOption({ series })
    }
  })
  myChart.on('mouseout', params => {
    if (params.seriesType === 'line') {
      const series = props.series.map(item => ({
        label: {
          show: false
        }
      }))
      myChart.setOption({ series })
    }
  })
})

// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  myChart.clear()
  if (props.series.length === 0) return

  let title = props?.title
  contentTip.value = titleTipObj[title] || ''
  let legend = {
    type: 'scroll',
    bottom: 0,
    right: 0,
    data: (series => {
      if (props.reverseLegend) series = series.reverse()
      // 提取系列名称
      const seriesNames = series.map(item => item.name)
      // 使用新函数将"其他"或"其它"排到第一位
      return sortLegendWithOtherFirst(seriesNames)
    })(JSON.parse(JSON.stringify(props.series))),
    ...props.legend
  }
  const option = {
    // title: getTitle(props.title),
    legend: legend,
    tooltip: JSON.parse(JSON.stringify(tooltip)),
    grid: props.grid,
    xAxis: [
      {
        type: 'category',
        position: 'bottom', // 确保x轴在底部
        axisLabel: {
          fontSize: 12,
          color: '#44546A',
          rotate: props.titleRotate ? 45 : 0
        },
        axisTick: {
          alignWithLabel: true,
          show: true //显示x轴刻度
        },
        axisLine: {
          onZero: false, // 不让x轴跟随y=0位置，强制在底部显示
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        data: getXAxisData(props.series)
      }
    ],
    yAxis: [
      {
        name: props.yAxisName,
        type: 'value',
        axisLabel: props.yAxisLabelFormate
          ? {
              formatter: props.yAxisLabelFormate,
              color: '#44546A'
            }
          : {
              color: '#44546A'
            },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        splitLine: {
          show: false // 隐藏分割线
        }
      }
    ],
    series: initSeries(props.series)
  }
  option.tooltip.formatter = params => {
    return formatter(params, ref([]), props.tooltipUnits, props, false)
  }
  myChart.setOption(option)
}

// 生成符合视图的数据
function initSeries(data) {
  const itemColor = props.color
  const series = []

  // 获取完整的x轴月份序列
  const xAxisMonths = getXAxisData(data)

  data.forEach((el, index) => {
    let colorTmp = colorMap[el.name] ? colorMap[el.name] : itemColor[index % color.length]

    //玉柴集团的图标显示阴影
    const itemStyle = {}
    const lineStyle = {}
    if (el.name.includes('玉柴')) {
      // itemStyle.shadowBlur = 6
      // // itemStyle.shadowColor = colorTmp
      // itemStyle.shadowOffsetX = 0
      // itemStyle.shadowOffsetY = 0
      // itemStyle.opacity = 1
      // lineStyle.shadowBlur = 6
      // // lineStyle.shadowColor = colorTmp
      // lineStyle.shadowOffsetX = 0
      // lineStyle.shadowOffsetY = 0
      // lineStyle.opacity = 1

      colorTmp = '#E72331'
    }

    // 根据完整的x轴月份序列重新排列数据
    const alignedData = alignDataToXAxis(el.data, xAxisMonths)

    series.push({
      type: 'line',
      symbol: 'circle',
      symbolSize: 6,
      ...el,
      name: el.name,
      triggerLineEvent: true,
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
        ...itemStyle,
        color: colorTmp
      },
      lineStyle: {
        ...lineStyle
      },
      data: alignedData,
      z: 1
    })
  })
  return series
}

// 将数据对齐到完整的x轴月份序列
function alignDataToXAxis(seriesData, xAxisMonths) {
  return xAxisMonths.map(month => {
    // 在原始数据中查找对应月份的数据
    const foundData = seriesData.find(item => item.name === month)
    return foundData ? foundData.value : null // 没有数据的月份显示为null（空值）
  })
}
function getTitle(tit) {
  let title = {}
  if (tit !== '' && tit !== undefined && tit !== null) {
    title = {
      text: tit,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 400,
        color: '#051C2C'
      }
    }
  }
  return title
}
// function getXAxisData(value) {
//   const data = value[0] ? (value[0].data ? value[0].data : []) : []
//
//   // 如果没有数据，返回空数组
//   if (data.length === 0) {
//     return []
//   }
//
//   // 提取所有月份名称并解析月份数字
//   const monthNames = data.map(v => v.name).filter(name => name && name.includes('月'))
//
//   if (monthNames.length === 0) {
//     return data.map(v => v.name)
//   }
//
//   // 解析月份数字
//   const monthNumbers = monthNames.map(name => {
//     const match = name.match(/(\d+)月/)
//     return match ? parseInt(match[1]) : null
//   }).filter(num => num !== null)
//
//   if (monthNumbers.length === 0) {
//     return data.map(v => v.name)
//   }
//
//   // 获取最大月份，从1月开始补齐到最大月份
//   const maxMonth = Math.max(...monthNumbers)
//
//   // 生成从1月到最大月份的完整序列
//   const completeMonths = []
//   for (let i = 1; i <= maxMonth; i++) {
//     completeMonths.push(i + '月')
//   }
//
//   return completeMonths
// }

function getXAxisData(value) {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  // 如果没有数据，返回空数组
  if (data.length === 0) {
    return []
  }

  // 获取所有的 name 值
  const allNames = data.map(v => v.name).filter(name => name)

  // 判断是否包含月份相关字样（月、月份）
  const hasMonthData = allNames.some(name =>
      name.includes('年')
  )

  // 如果不包含月份相关字样，直接返回原始数据
  if (hasMonthData) {
    return allNames
  }

  // 包含月份数据，进行月份处理
  // 提取所有月份名称并解析月份数字
  const monthNames = allNames.filter(name => name.includes('月'))

  if (monthNames.length === 0) {
    return allNames
  }

  // 解析月份数字
  const monthNumbers = monthNames.map(name => {
    const match = name.match(/(\d+)月/)
    return match ? parseInt(match[1]) : null
  }).filter(num => num !== null)

  if (monthNumbers.length === 0) {
    return data.map(v => v.name)
  }

  // 获取最大月份，从1月开始补齐到最大月份
  const maxMonth = Math.max(...monthNumbers)

  // 生成从1月到最大月份的完整序列
  const completeMonths = []
  for (let i = 1; i <= maxMonth; i++) {
    completeMonths.push(i + '月')
  }

  return completeMonths
}
</script>
