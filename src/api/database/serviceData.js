import request from '@/utils/request'
const PREFIX = 'intelligence'
// 查询列表
export function competitorList(data) {
  return request({
    url: `${PREFIX}/service-data/list`,
    method: 'post',
    data
  })
}
// 导入
export function importData(query) {
    return request({
      url: `${PREFIX}/competitor/importData`,
      method: 'get',
      params: query
    })
}

/**
 * @description 数据库管理-行业数据-中内协列表
 * @returns Object
 */
export const cnEnginOrgList = (data) => {
  return request({
    url: `${PREFIX}/cnEnginOrg/pageQuery`,
    method: 'post',
    data
  })
}

/**
 * @description 数据库管理-行业数据-海关数列表
 * @returns Object
 */
export const customsList = (data) => {
  return request({
    url: `${PREFIX}/customs/pageQuery`,
    method: 'post',
    data
  })
}

/**
 * @description 数据库管理-行业数据-装机数列表
 * @returns Object
 */
export const installList = (data) => {
  return request({
    url: `${PREFIX}/install/pageQuery`,
    method: 'post',
    data
  })
}

/**
 * @description 数据库管理-行业数据-上险数列表
 * @returns Object
 */
export const insureList = (data) => {
  return request({
    url: `${PREFIX}/insure/pageQuery`,
    method: 'post',
    data
  })
}

/**
 * @description 数据库管理-行业数据-货运新增数列表
 * @returns Object
 */
export const corNewFreightList = (data) => {
  return request({
    url: `${PREFIX}/corNewFreight/pageQuery`,
    method: 'post',
    data
  })
}

/**
 * @description 数据库管理-行业数据-船电数列表
 * @returns Object
 */
export const shipList = (data) => {
  return request({
    url: `${PREFIX}/ship/pageQuery`,
    method: 'post',
    data
  })
}

/**
 * @description 数据库管理-行业数据-流向数
 * @returns Object
 */
export const flowDataList = (data) => {
  return request({
    url: `${PREFIX}/flowData/pageQuery`,
    method: 'post',
    data
  })
}

export const temporarySignature = (contentType) => {
  return request({
    url: `/file/obs/temporarySignature?contentType=${contentType}`,
    method: 'post',
    data
  })
}


/**
 * @description 数据库管理-业务数据-删除数据
 * @returns Object
 */
export const serverDataDelete = (data) => {
  return request({
    url: `${PREFIX}/service-data/deleteData`,
    method: 'post',
    data
  })
}