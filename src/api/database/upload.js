import request from '@/utils/request'

// 查询列表
export function list(query) {
  return request({
    url: '/intelligence/file/list',
    method: 'get',
    params: query
  })
}
// 详情
export function detail(query) {
  return request({
    url: `/intelligence/file/detail/${query}`,
    method: 'get'
  })
}
// 新增//修改
export function addupdate(query, adata) {
  return request({
    url: `/intelligence/file/${adata}`,
    method: 'post',
    data: query
  })
}

// 删除
export function del(query) {
  return request({
    url: `/intelligence/file/del/${query}`,
    method: 'delete'
  })
}

/**
 * 选择未授权用户
 * @param {*} id
 * @returns
 */
export function selectNotFileUser(data) {
  return request({
    url: `/intelligence/file/selectNotFileUser`,
    method: 'post',
    data,
    params: {
      "pageNum":data?.pageNum,
      "pageSize":data?.pageSize,
      "nickName":data?.nickName,
      "roleId":data?.roleId,
      "commandKey":data?.commandKey
    }
  })
}

export function setFileUser(data) {
  return request({
    url: `/intelligence/file/setFileUser`,
    method: 'post',
    data,
    
  })
}

export function selectFileUser(data) {
  return request({
    url: `/intelligence/file/selectFileUser`,
    method: 'post',
    data,
    params: data
  })
}
// 删除文件授权访问用户
export function deleteFileUser(data) {
  return request({
    url: `/intelligence/file/deleteFileUser`,
    method: 'post',
    data
  })
}
