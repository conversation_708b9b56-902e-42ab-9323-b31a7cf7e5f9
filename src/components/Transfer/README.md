# Transfer 穿梭框组件

一个功能完整的穿梭框组件，支持搜索、全选、批量移动等功能。

## 功能特性

- ✅ 支持姓名和角色搜索
- ✅ 支持全选/取消全选
- ✅ 支持批量移动
- ✅ 左右面板高度自适应
- ✅ 响应式设计
- ✅ 可配置字段映射
- ✅ 支持禁用状态
- ✅ 事件回调

## 基础用法

```vue
<template>
  <Transfer
    v-model="selectedUsers"
    :data-source="allUsers"
    :search-config="searchConfig"
    :display-config="displayConfig"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import Transfer from '@/components/Transfer/index.vue'

const allUsers = ref([
  { id: 1, name: '张三', department: '前端开发', role: '技术部' },
  { id: 2, name: '李四', department: '后端开发', role: '技术部' },
])

const selectedUsers = ref([])

const searchConfig = ref({
  nameField: 'name',
  namePlaceholder: '按姓名搜索',
  roleField: 'role',
  rolePlaceholder: '按角色搜索',
  roleOptions: [
    { label: '技术部', value: '技术部' },
    { label: '设计部', value: '设计部' },
    // ...更多选项
  ]
})

const displayConfig = ref({
  nameField: 'name',
  departmentField: 'department',
  roleField: 'role',
  keyField: 'id'
})

const handleChange = (value, action, items) => {
  console.log('数据变化:', { value, action, items })
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 已选中的数据 | Array | [] |
| dataSource | 数据源 | Array | [] |
| leftTitle | 左侧标题 | String | '未选中人员' |
| rightTitle | 右侧标题 | String | '已选中人员' |
| searchConfig | 搜索配置 | Object | 见下方 |
| displayConfig | 显示字段配置 | Object | 见下方 |
| disabled | 是否禁用 | Boolean | false |
| inDialog | 是否在弹窗中使用 | Boolean | false |
| height | 容器高度（用于弹窗适配） | String/Number | null |

### searchConfig 配置

```javascript
{
  nameField: 'name',           // 姓名字段
  namePlaceholder: '按姓名搜索', // 姓名搜索框占位符
  roleField: 'role',           // 角色字段
  rolePlaceholder: '按角色搜索', // 角色搜索框占位符
  roleOptions: []              // 角色选项数组
}
```

### displayConfig 配置

```javascript
{
  nameField: 'name',           // 姓名字段
  departmentField: 'department', // 部门字段
  roleField: 'role',           // 角色字段
  keyField: 'id'               // 唯一标识字段
}
```

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 选中数据变化时触发 | (value: Array) |
| change | 数据变化时触发 | (value: Array, action: String, items: Array) |

### change 事件参数说明

- `value`: 当前已选中的数据
- `action`: 操作类型，'add' 表示添加，'remove' 表示移除
- `items`: 本次操作涉及的数据项

## 高级用法

### 自定义标题

```vue
<Transfer
  v-model="selectedUsers"
  :data-source="allUsers"
  left-title="候选人员"
  right-title="已选人员"
/>
```

### 禁用状态

```vue
<Transfer
  v-model="selectedUsers"
  :data-source="allUsers"
  :disabled="true"
/>
```

### 在弹窗中使用

```vue
<template>
  <el-dialog title="选择人员" v-model="dialogVisible" width="60%">
    <Transfer
      v-model="selectedUsers"
      :data-source="allUsers"
      :in-dialog="true"
      :search-config="searchConfig"
      :display-config="displayConfig"
    />
  </el-dialog>
</template>
```

### 自定义高度

```vue
<Transfer
  v-model="selectedUsers"
  :data-source="allUsers"
  :height="500"
  :in-dialog="true"
/>
```

### 自定义字段映射

```vue
<Transfer
  v-model="selectedUsers"
  :data-source="allUsers"
  :display-config="{
    nameField: 'userName',
    departmentField: 'dept',
    roleField: 'position',
    keyField: 'userId'
  }"
/>
```

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

1. 使用 CSS 变量（如果组件支持）
2. 使用深度选择器 `:deep()`
3. 覆盖组件的 class

```vue
<style>
.transfer-container {
  /* 自定义容器样式 */
}

:deep(.transfer-panel) {
  /* 自定义面板样式 */
}
</style>
```

## 注意事项

1. `dataSource` 中的每个对象必须包含 `keyField` 指定的唯一标识字段
2. `roleOptions` 数组中的每个选项需要包含 `label` 和 `value` 字段
3. 组件会自动处理数据的去重和状态同步
4. 建议在大数据量场景下使用虚拟滚动优化性能

## 更新日志

### v1.1.0
- 新增 `inDialog` 属性，支持在弹窗中使用
- 新增 `height` 属性，支持自定义容器高度
- 优化弹窗中的样式，解决宽度溢出问题
- 改进响应式设计，适配不同屏幕尺寸

### v1.0.0

- 初始版本
- 支持基础的穿梭框功能
- 支持搜索和全选
- 响应式设计
