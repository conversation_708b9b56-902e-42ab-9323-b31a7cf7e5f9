
<template>
  <div class="transfer-container" :class="{ 'in-dialog': inDialog }">
    <!-- 左侧未选中人员 -->
    <div class="transfer-panel left-panel">
      <div class="panel-header">
        <h3>{{ leftTitle }}</h3>
      </div>
      
      <!-- 搜索框区域 -->
      <div class="search-area">
        <div class="search-row">
          <el-input
            v-model="nameSearch"
            :placeholder="searchConfig.namePlaceholder"
            prefix-icon="Search"
            clearable
            class="search-input"
            :disabled="disabled"
            @input="handleNameSearch"
            @clear="handleNameSearchClear"
          />
          <el-select-v2
            v-model="roleSearch"
            :placeholder="searchConfig.rolePlaceholder"
            clearable
            filterable
            remote
            :remote-method="handleRoleRemoteSearch"
            class="search-select"
            :disabled="disabled"
            :options="filteredRoleOptions"
            @change="handleRoleChange"
            @clear="handleRoleClear"
          />
        </div>
      </div>

      <!-- 全选区域 -->
      <div class="select-all-area">
        <el-checkbox
          v-model="leftSelectAll"
          :indeterminate="leftIndeterminate"
          @change="handleLeftSelectAll"
          :disabled="disabled"
        >
          全选
        </el-checkbox>
        <span class="count">{{ leftCheckedCount }}/{{   filteredLeftData.length }} 总数：{{ leftTotal }}</span>
      </div>

      <!-- 人员列表 -->
      <div class="user-list" ref="leftListRef">
        <el-scrollbar
          ref="leftScrollbarRef"
          height="300px"
          class="list-scrollbar"
        >
          <div
            v-loading="refreshing"
            element-loading-text="刷新中..."
            class="list-content"
          >
            <el-checkbox-group v-model="leftChecked" :disabled="disabled">
              <div
                v-for="item in filteredLeftData"
                :key="item[displayConfig.keyField]"
                class="user-item"
              >
                <el-checkbox :label="item[displayConfig.keyField]" class="user-checkbox">
                  <div class="user-info">
                    <div class="user-name">{{ item[displayConfig.nameField] }}</div>
                    <div class="user-role">{{ item[displayConfig.departmentField] }} · {{ item[displayConfig.roleField] }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 中间操作区域 -->
    <div class="transfer-arrows">
      <div class="transfer-buttons-group">
        <el-button
          type="primary"
          :disabled="disabled || leftChecked.length === 0"
          @click="moveToRight"
          class="transfer-btn"
        >
          添加
          <el-icon><ArrowRight /></el-icon>
        </el-button>
        <el-button
          type="primary"
          :disabled="disabled || rightChecked.length === 0"
          @click="moveToLeft"
          class="transfer-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          移除
        </el-button>
      </div>
    </div>

    <!-- 右侧已选中人员 -->
    <div class="transfer-panel right-panel">
      <div class="panel-header">
        <h3>{{ rightTitle }}</h3>
      </div>

      <!-- 全选区域 -->
      <div class="select-all-area">
        <el-checkbox
          v-model="rightSelectAll"
          :indeterminate="rightIndeterminate"
          @change="handleRightSelectAll"
          :disabled="disabled"
        >
          全选
        </el-checkbox>
        <span class="count">{{ rightCheckedCount }}/{{ rightTotal || rightData.length }}</span>
      </div>

      <!-- 已选人员列表 -->
      <div class="user-list">
        <el-scrollbar height="360px" class="list-scrollbar">
          <div class="list-content">
            <el-checkbox-group v-model="rightChecked" :disabled="disabled">
              <div
                v-for="item in rightData"
                :key="item[displayConfig.keyField]"
                class="user-item"
              >
                <el-checkbox :label="item[displayConfig.keyField]" class="user-checkbox">
                  <div class="user-info">
                    <div class="user-name">{{ item[displayConfig.nameField] }}</div>
                    <div class="user-role">{{ item[displayConfig.departmentField] }} · {{ item[displayConfig.roleField] }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ArrowRight, ArrowLeft } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  leftTotal:{
    type: Number,
    default: null
  },
  rightTotal:{
    type: Number,
    default: null
  },
  // 数据源
  dataSource: {
    type: Array,
    default: () => []
  },
  // 已选中的数据
  modelValue: {
    type: Array,
    default: () => []
  },
  // 左侧标题
  leftTitle: {
    type: String,
    default: '未选中人员'
  },
  // 右侧标题
  rightTitle: {
    type: String,
    default: '已选中人员'
  },
  // 搜索配置
  searchConfig: {
    type: Object,
    default: () => ({
      nameField: 'name',
      namePlaceholder: '按姓名搜索',
      roleField: 'role',
      rolePlaceholder: '按角色搜索',
      roleOptions: []
    })
  },
  // 显示字段配置
  displayConfig: {
    type: Object,
    default: () => ({
      nameField: 'name',
      departmentField: 'department',
      roleField: 'role',
      keyField: 'id'
    })
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 容器高度（用于弹窗适配）
  height: {
    type: [String, Number],
    default: null
  },
  // 是否在弹窗中使用
  inDialog: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'update:modelValue',
  'change',
  'refresh',
  'name-search',
  'role-change',
  'role-search'
])

// 搜索条件
const nameSearch = ref('')
const roleSearch = ref('')
const roleSearchKeyword = ref('')

// 左右两侧数据
const leftData = ref([])
const rightData = ref([])

// 选中的项目
const leftChecked = ref([])
const rightChecked = ref([])

// 下拉刷新相关
const refreshing = ref(false)
const leftListRef = ref()
const leftScrollbarRef = ref()

// 防重复触发相关
const lastTriggerTime = ref(0)
const isAtBottomTriggered = ref(false)
const scrollThrottleTimer = ref(null)

// 初始化数据
const initData = () => {
  const selectedIds = props.modelValue.map(item => item[props.displayConfig.keyField])
  leftData.value = props.dataSource.filter(item => !selectedIds.includes(item[props.displayConfig.keyField]))
  rightData.value = [...props.modelValue]
}

// 监听 props 变化
watch(() => props.dataSource, initData, { immediate: true })
watch(() => props.modelValue, initData, { immediate: true })

// 过滤后的左侧数据
const filteredLeftData = computed(() => {
  return leftData.value.filter(item => {
    const nameMatch = !nameSearch.value || item[props.searchConfig.nameField]?.includes(nameSearch.value)
    // 注释掉角色过滤，因为现在角色选择是用来请求数据的，不是用来过滤的
    // const roleMatch = !roleSearch.value || item[props.searchConfig.roleField] === roleSearch.value
    return nameMatch // && roleMatch
  })
})

// 过滤后的角色选项
const filteredRoleOptions = computed(() => {
  if (!roleSearchKeyword.value) {
    return props.searchConfig.roleOptions
  }
  return props.searchConfig.roleOptions.filter(option =>
    option.label.toLowerCase().includes(roleSearchKeyword.value.toLowerCase())
  )
})

// 左侧全选状态
const leftSelectAll = computed({
  get() {
    return filteredLeftData.value.length > 0 && leftChecked.value.length === filteredLeftData.value.length
  },
  set(value) {
    if (value) {
      leftChecked.value = filteredLeftData.value.map(item => item[props.displayConfig.keyField])
    } else {
      leftChecked.value = []
    }
  }
})

// 右侧全选状态
const rightSelectAll = computed({
  get() {
    return rightData.value.length > 0 && rightChecked.value.length === rightData.value.length
  },
  set(value) {
    if (value) {
      rightChecked.value = rightData.value.map(item => item[props.displayConfig.keyField])
    } else {
      rightChecked.value = []
    }
  }
})

// 半选状态
const leftIndeterminate = computed(() => {
  const checkedCount = leftChecked.value.length
  return checkedCount > 0 && checkedCount < filteredLeftData.value.length
})

const rightIndeterminate = computed(() => {
  const checkedCount = rightChecked.value.length
  return checkedCount > 0 && checkedCount < rightData.value.length
})

// 选中数量
const leftCheckedCount = computed(() => leftChecked.value.length)
const rightCheckedCount = computed(() => rightChecked.value.length)

// 计算容器和滚动区域高度
const containerHeight = computed(() => {
  if (props.height) {
    return typeof props.height === 'number' ? `${props.height}px` : props.height
  }
  return props.inDialog ? '400px' : '600px'
})

const scrollHeight = computed(() => {
  if (props.height) {
    // 如果指定了容器高度，滚动区域高度需要减去头部区域的高度
    const baseHeight = typeof props.height === 'number' ? props.height : parseInt(props.height)
    return `${Math.max(baseHeight - 200, 200)}px` // 减去头部约200px，最小200px
  }
  return props.inDialog ? '200px' : '320px'
})

const rightScrollHeight = computed(() => {
  if (props.height) {
    const baseHeight = typeof props.height === 'number' ? props.height : parseInt(props.height)
    return `${Math.max(baseHeight - 140, 200)}px` // 右侧没有搜索框，减去约140px
  }
  return props.inDialog ? '260px' : '380px'
})

// 处理全选
const handleLeftSelectAll = (value) => {
  leftSelectAll.value = value
}

const handleRightSelectAll = (value) => {
  rightSelectAll.value = value
}

// 移动到右侧
const moveToRight = () => {
  if (props.disabled) return
  
  const itemsToMove = leftData.value.filter(item => 
    leftChecked.value.includes(item[props.displayConfig.keyField])
  )
  rightData.value.push(...itemsToMove)
  leftData.value = leftData.value.filter(item => 
    !leftChecked.value.includes(item[props.displayConfig.keyField])
  )
  leftChecked.value = []
  
  // 触发事件
  emit('update:modelValue', rightData.value)
  emit('change', rightData.value, 'add', itemsToMove)
}

// 移动到左侧
const moveToLeft = () => {
  if (props.disabled) return

  const itemsToMove = rightData.value.filter(item =>
    rightChecked.value.includes(item[props.displayConfig.keyField])
  )
  leftData.value.push(...itemsToMove)
  rightData.value = rightData.value.filter(item =>
    !rightChecked.value.includes(item[props.displayConfig.keyField])
  )
  rightChecked.value = []

  // 触发事件
  emit('update:modelValue', rightData.value)
  emit('change', rightData.value, 'remove', itemsToMove)
}

// 处理姓名搜索
const handleNameSearch = (value) => {
  emit('name-search', value)
}

// 处理姓名搜索清空
const handleNameSearchClear = () => {
  emit('name-search', '')
}

// 处理角色选择变化
const handleRoleChange = (value) => {
  // 当角色选择变化时，清空搜索关键词
  roleSearchKeyword.value = ''
  emit('role-change', value)
}

// 处理角色选择清空
const handleRoleClear = () => {
  roleSearchKeyword.value = ''
  // 清空时传递 null 或空值，让父组件知道要加载所有数据
  emit('role-change', null)
}

// 处理角色远程搜索
const handleRoleRemoteSearch = (query) => {
  roleSearchKeyword.value = query
  // 也可以触发事件给父组件处理远程搜索
  emit('role-search', query)
}



// 触发刷新
const triggerRefresh = () => {
  if (refreshing.value) return

  refreshing.value = true
  emit('refresh', () => {
    refreshing.value = false
    // 刷新完成后，允许再次触发（但仍需要离开底部再回到底部）
    console.log('刷新完成，重置状态')
  })
}

// 节流处理滚动事件
const throttledScrollHandler = (event) => {
  if (scrollThrottleTimer.value) {
    clearTimeout(scrollThrottleTimer.value)
  }

  scrollThrottleTimer.value = setTimeout(() => {
    handleScrollToBottom(event)
  }, 100) // 100ms 节流
}

// 设置滚动事件监听器
onMounted(async () => {
  // 等待 DOM 渲染完成
  await nextTick()

  // 获取滚动容器并添加事件监听器
  const scrollContainer = leftScrollbarRef.value?.wrapRef
  if (scrollContainer) {
    console.log('添加滚动事件监听器')
    scrollContainer.addEventListener('scroll', throttledScrollHandler)
  } else {
    console.warn('未找到滚动容器')
  }
})

onUnmounted(() => {
  // 清理事件监听器和定时器
  const scrollContainer = leftScrollbarRef.value?.wrapRef
  if (scrollContainer) {
    scrollContainer.removeEventListener('scroll', throttledScrollHandler)
  }

  if (scrollThrottleTimer.value) {
    clearTimeout(scrollThrottleTimer.value)
  }
})

// 处理滚动到底部
const handleScrollToBottom = (event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target
  const currentTime = Date.now()

  // 检查是否滚动到底部（10px 容差）
  const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10

  if (isAtBottom) {
    // 如果已经在底部但还没触发过，或者距离上次触发超过2秒
    if (!isAtBottomTriggered.value && !refreshing.value && (currentTime - lastTriggerTime.value > 2000)) {
      console.log('滚动到底部了，触发刷新')
      isAtBottomTriggered.value = true
      lastTriggerTime.value = currentTime
      triggerRefresh()
    }
  } else {
    // 如果不在底部，重置触发状态
    if (isAtBottomTriggered.value) {
      isAtBottomTriggered.value = false
      console.log('离开底部，重置触发状态')
    }
  }
}
</script>

<style scoped>
:deep(.el-button + .el-button){
  margin: 0;
}
.transfer-container {
  display: flex;
  gap: 20px;
  /* padding: 20px; */
  min-height: 600px;
  align-items: stretch;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 在弹窗中使用时的样式调整 */
.transfer-container.in-dialog {
  min-height: 400px;
  gap: 15px;
}

.transfer-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 180px;
  max-width: calc(50% - 60px); /* 确保两个面板加上中间区域不会溢出 */
  /* background-color: rebeccapurple; */
  height: 100%;
  min-height: 500px;
  overflow: hidden;
}

/* 在弹窗中使用时的面板样式调整 */
.transfer-container.in-dialog .transfer-panel {
  min-width: 150px;
  min-height: 400px;
  max-width: calc(50% - 50px);
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.search-area {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
}

/* 在弹窗中使用时的搜索区域样式调整 */
.transfer-container.in-dialog .search-area {
  padding: 12px 16px;
}

.search-row {
  display: flex;
  gap: 12px;
}

/* 在弹窗中使用时的搜索行样式调整 */
.transfer-container.in-dialog .search-row {
  gap: 8px;
}

.search-input {
  flex: 1;
  min-width: 0; /* 允许输入框收缩 */
}

.search-select {
  width: 140px;
  flex-shrink: 0;
}

/* 在弹窗中使用时的搜索选择框样式调整 */
.transfer-container.in-dialog .search-select {
  width: 120px;
}

.select-all-area {
  padding: 12px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.count {
  color: #666;
  font-size: 14px;
}

.user-list {
  flex: 1;
  min-height: 0;
}

.list-scrollbar {
  width: 100%;
}

.list-content {
  padding: 0;
}

.user-item {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.user-item:hover {
  background-color: #f8f9fa;
}

.user-item:last-child {
  border-bottom: none;
}

.user-checkbox {
  width: 100%;
}

.user-checkbox :deep(.el-checkbox__label) {
  width: 100%;
  padding-left: 8px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.user-role {
  font-size: 12px;
  color: #666;
}

.transfer-arrows {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  flex-shrink: 0;
  min-width: 120px;
}

/* 在弹窗中使用时的操作区域样式调整 */
.transfer-container.in-dialog .transfer-arrows {
  padding: 15px 0;
  min-width: 100px;
}

.transfer-buttons-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.transfer-container.in-dialog .transfer-buttons-group {
  gap: 15px;
}

.transfer-btn {
  min-width: 120px;
  white-space: nowrap;
}

.transfer-container.in-dialog .transfer-btn {
  min-width: 80px;
  font-size: 14px;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .transfer-container {
    flex-direction: column;
    gap: 16px;
  }

  .transfer-panel {
    max-width: none;
  }

  .transfer-arrows {
    min-height: auto;
    padding: 16px 0;
  }

  .transfer-buttons-group {
    flex-direction: row;
    gap: 16px;
  }

  .search-row {
    flex-direction: column;
    gap: 8px;
  }

  .search-select {
    width: 100%;
  }

  .transfer-btn {
    min-width: 100px;
    font-size: 12px;
  }
}

/* 弹窗中的响应式设计 */
@media (max-width: 1200px) {
  .transfer-container.in-dialog {
    gap: 10px;
  }

  .transfer-container.in-dialog .transfer-panel {
    min-width: 120px;
    max-width: calc(50% - 40px);
  }

  .transfer-container.in-dialog .transfer-arrows {
    min-width: 80px;
  }

  .transfer-container.in-dialog .search-row {
    flex-direction: column;
    gap: 6px;
  }

  .transfer-container.in-dialog .search-select {
    width: 100%;
  }
}
</style>
