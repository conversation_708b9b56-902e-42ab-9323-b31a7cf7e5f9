# TableTransfer 表格穿梭框组件

一个基于表格的穿梭框组件，支持分页、搜索、批量选择等功能。

## 功能特性

- ✅ 左右表格布局，支持分页
- ✅ 支持姓名和角色搜索
- ✅ 支持批量选择和移动
- ✅ 左右两侧独立分页
- ✅ 响应式设计
- ✅ 可配置字段映射
- ✅ 支持禁用状态
- ✅ 事件回调

## 基础用法

```vue
<template>
  <TableTransfer
    v-model="selectedUsers"
    :data-source="allUsers"
    :search-config="searchConfig"
    :display-config="displayConfig"
    @change="handleChange"
    @left-page-change="handlePageChange"
    @left-size-change="handleSizeChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import TableTransfer from '@/components/TableTransfer/index.vue'

const allUsers = ref([
  { id: 1, name: '张三', department: '前端开发', role: '技术部' },
  { id: 2, name: '李四', department: '后端开发', role: '技术部' },
])

const selectedUsers = ref([])

const searchConfig = ref({
  nameField: 'name',
  namePlaceholder: '按姓名搜索',
  roleField: 'role',
  rolePlaceholder: '按角色搜索',
  roleOptions: [
    { label: '技术部', value: '技术部' },
    { label: '设计部', value: '设计部' },
  ]
})

const displayConfig = ref({
  nameField: 'name',
  departmentField: 'department',
  roleField: 'role',
  keyField: 'id'
})

const handleChange = (value, action, items) => {
  console.log('数据变化:', { value, action, items })
}

const handlePageChange = (page) => {
  console.log('分页变化:', page)
  // 处理分页逻辑
}

const handleSizeChange = (size) => {
  console.log('分页大小变化:', size)
  // 处理分页大小变化逻辑
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 已选中的数据 | Array | [] |
| dataSource | 数据源 | Array | [] |
| leftTitle | 左侧标题 | String | '未选中人员' |
| rightTitle | 右侧标题 | String | '已选中人员' |
| searchConfig | 搜索配置 | Object | 见下方 |
| displayConfig | 显示字段配置 | Object | 见下方 |
| disabled | 是否禁用 | Boolean | false |
| inDialog | 是否在弹窗中使用 | Boolean | false |
| leftTotal | 左侧数据总数 | Number | 0 |

### searchConfig 配置

```javascript
{
  nameField: 'name',           // 姓名字段
  namePlaceholder: '按姓名搜索', // 姓名搜索框占位符
  roleField: 'role',           // 角色字段
  rolePlaceholder: '按角色搜索', // 角色搜索框占位符
  roleOptions: []              // 角色选项数组
}
```

### displayConfig 配置

```javascript
{
  nameField: 'name',           // 姓名字段
  departmentField: 'department', // 部门字段
  roleField: 'role',           // 角色字段
  keyField: 'id'               // 唯一标识字段
}
```

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 选中数据变化时触发 | (value: Array) |
| change | 数据变化时触发 | (value: Array, action: String, items: Array) |
| refresh | 刷新事件 | (callback: Function) |
| name-search | 姓名搜索事件 | (value: String) |
| role-change | 角色选择变化事件 | (value: String) |
| role-search | 角色搜索事件 | (query: String) |
| left-page-change | 左侧分页变化事件 | (page: Number) |
| left-size-change | 左侧分页大小变化事件 | (size: Number) |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| triggerRefresh | 触发刷新 | - |
| clearSelection | 清空所有选择 | - |

## 高级用法

### 在弹窗中使用

```vue
<template>
  <el-dialog title="选择人员" v-model="dialogVisible" width="80%">
    <TableTransfer
      v-model="selectedUsers"
      :data-source="allUsers"
      :in-dialog="true"
      :search-config="searchConfig"
      :display-config="displayConfig"
    />
  </el-dialog>
</template>
```

### 自定义标题

```vue
<TableTransfer
  v-model="selectedUsers"
  :data-source="allUsers"
  left-title="候选人员"
  right-title="已选人员"
/>
```

### 禁用状态

```vue
<TableTransfer
  v-model="selectedUsers"
  :data-source="allUsers"
  :disabled="true"
/>
```

### 处理分页

```vue
<template>
  <TableTransfer
    v-model="selectedUsers"
    :data-source="allUsers"
    :leftTotal="totalCount"
    @left-page-change="handlePageChange"
    @left-size-change="handleSizeChange"
  />
</template>

<script setup>
const handlePageChange = async (page) => {
  // 加载指定页的数据
  const response = await fetchUsers({ page, pageSize: 10 })
  allUsers.value = response.data
}

const handleSizeChange = async (size) => {
  // 改变分页大小，重新加载第一页
  const response = await fetchUsers({ page: 1, pageSize: size })
  allUsers.value = response.data
}
</script>
```

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<style>
.table-transfer-container {
  /* 自定义容器样式 */
}

:deep(.transfer-panel) {
  /* 自定义面板样式 */
}

:deep(.el-table) {
  /* 自定义表格样式 */
}
</style>
```

## 注意事项

1. `dataSource` 中的每个对象必须包含 `keyField` 指定的唯一标识字段
2. `roleOptions` 数组中的每个选项需要包含 `label` 和 `value` 字段
3. 组件会自动处理数据的去重和状态同步
4. 分页功能需要配合后端接口实现
5. 表格高度在弹窗模式下会自动调整

## 更新日志

### v1.0.0

- 初始版本
- 支持基础的表格穿梭框功能
- 支持分页、搜索和批量选择
- 响应式设计
- 支持弹窗模式
