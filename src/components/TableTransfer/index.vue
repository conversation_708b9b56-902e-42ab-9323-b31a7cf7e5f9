<template>
  <div class="table-transfer-container" :class="{ 'in-dialog': inDialog }">
    <!-- 左侧未选中人员表格 -->
    <div class="transfer-panel left-panel">
      <div class="panel-header">
        <h3>{{ leftTitle }}</h3>
      </div>

      <!-- 搜索框区域 -->
      <div class="search-area">
        <div class="search-row">
          <el-input
            v-model="nameSearch"
            :placeholder="searchConfig.namePlaceholder"
            prefix-icon="Search"
            clearable
            class="search-input"
            :disabled="disabled"
            @input="handleNameSearch"
            @clear="handleNameSearchClear"
          />
          <el-select-v2
            v-model="roleSearch"
            :placeholder="searchConfig.rolePlaceholder"
            clearable
            filterable
            remote
            :remote-method="handleRoleRemoteSearch"
            class="search-select"
            :disabled="disabled"
            :options="filteredRoleOptions"
            @change="handleRoleChange"
            @clear="handleRoleClear"
          />
        </div>
      </div>

      <!-- 左侧表格 -->
      <div class="table-container">
        <el-table
          ref="leftTableRef"
          :data="leftTableData"
          v-loading="loading || refreshing"
          element-loading-text="加载中..."
          @selection-change="handleLeftSelectionChange"
          height="100%"
          :disabled="disabled"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column :prop="displayConfig.nameField" label="姓名" min-width="80" />
          <el-table-column :prop="displayConfig.departmentField" label="部门" min-width="100" />
          <el-table-column :prop="displayConfig.roleField" label="角色" min-width="80" />
        </el-table>

        <!-- 左侧分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="leftPagination.page"
            v-model:page-size="leftPagination.pageSize"
            :page-sizes="[5,10, 20, 50, 100]"
            :default-page-size="5"
            :total="leftPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleLeftSizeChange"
            @current-change="handleLeftPageChange"
            small
          />
        </div>
      </div>
    </div>

    <!-- 中间操作区域 -->
    <div class="transfer-arrows">
      <div class="transfer-buttons-group">
        <el-button
          type="primary"
          :disabled="disabled || leftSelection.length === 0"
          @click="moveToRight"
          class="transfer-btn"
        >
          添加
          <el-icon><ArrowRight /></el-icon>
        </el-button>
        <el-button
          type="primary"
          :disabled="disabled || rightSelection.length === 0"
          @click="moveToLeft"
          class="transfer-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          移除
        </el-button>
      </div>
    </div>

    <!-- 右侧已选中人员表格 -->
    <div class="transfer-panel right-panel">
      <div class="panel-header">
        <h3>{{ rightTitle }} ( {{ rightPagination.total | 0 }}人)</h3>
      </div>

      <!-- 右侧表格 -->
      <div class="table-container">
        <el-table
          ref="rightTableRef"
          :data="rightTableData"
          @selection-change="handleRightSelectionChange"
          height="100%"
          :disabled="disabled"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column :prop="displayConfig.nameField" label="姓名" min-width="80" />
          <el-table-column :prop="displayConfig.departmentField" label="部门" min-width="100" />
          <el-table-column :prop="displayConfig.roleField" label="角色" min-width="80" />
        </el-table>

        <!-- 右侧分页 -->
        <!-- <div class="pagination-container">
          <el-pagination
            v-model:current-page="rightPagination.page"
            v-model:page-size="rightPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="rightPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleRightSizeChange"
            @current-change="handleRightPageChange"
            small
          />
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ArrowRight, ArrowLeft } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  // 已选中的数据
  modelValue: {
    type: Array,
    default: () => []
  },
  // 左侧标题
  leftTitle: {
    type: String,
    default: '未选中人员'
  },
  // 右侧标题
  rightTitle: {
    type: String,
    default: '已选中人员'
  },
  // 搜索配置
  searchConfig: {
    type: Object,
    default: () => ({
      nameField: 'name',
      namePlaceholder: '按姓名搜索',
      roleField: 'role',
      rolePlaceholder: '按角色搜索',
      roleOptions: []
    })
  },
  // 显示字段配置
  displayConfig: {
    type: Object,
    default: () => ({
      nameField: 'name',
      departmentField: 'department',
      roleField: 'role',
      keyField: 'id'
    })
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否在弹窗中使用
  inDialog: {
    type: Boolean,
    default: false
  },
  // API 配置
  apiConfig: {
    type: Object,
    default: () => ({
      loadUsers: null, // 加载用户列表的API函数
      loadSelectedUsers: null, // 加载已选用户的API函数
      loadRoles: null, // 加载角色列表的API函数
      addSelectedUsers: null, // 添加选中用户的API函数
      removeSelectedUsers: null // 移除选中用户的API函数
    })
  },
  // 当前记录ID（用于编辑模式）
  recordId: {
    type: [String, Number],
    default: null
  }
})

// Emits 定义
const emit = defineEmits([
  'update:modelValue',
  'change',
  'refresh',
  'name-search',
  'role-change',
  'role-search',
  'left-page-change',
  'left-size-change'
])

// 搜索条件
const nameSearch = ref('')
const roleSearch = ref('')
const roleSearchKeyword = ref('')

// 表格数据
const leftTableData = ref([])
const rightTableData = ref([])

// 选中的数据
const leftSelection = ref([])
const rightSelection = ref([])

// 分页数据
const leftPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  serverTotal: 0 // 服务器返回的原始总数
})

const rightPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const refreshing = ref(false)
const loading = ref(false)

// 表格引用
const leftTableRef = ref()
const rightTableRef = ref()

// 角色选项数据
const roleOptions = ref([])

// 当前搜索参数
const currentSearchParams = ref({
  nickName: '',
  roleId: null
})

// 加载用户数据
const loadUsers = async (isLoadMore = false) => {
  console.log('加载1')
  if (!props.apiConfig.loadUsers) {
    console.warn('未配置 loadUsers API')
    return
  }
  console.log('loadUsers', currentSearchParams.value)

  try {
    loading.value = true
    const params = {
      pageNum: leftPagination.value.page,
      pageSize: leftPagination.value.pageSize,
      webSelectedUserIdList: rightTableData.value.map(item => item.id),
      ...currentSearchParams.value,
      ...(props.recordId && { id: props.recordId })
    }

    const response = await props.apiConfig.loadUsers(params)

    if (response && response.rows) {
      const newUsers = response.rows.map(item => ({
        id: item.userId,
        name: item.nickName,
        department: item.deptName,
        role: item.roleName
      }))

      if (isLoadMore) {
        leftTableData.value = [...leftTableData.value, ...newUsers]
      } else {
        leftTableData.value = newUsers
      }

      // 过滤掉已选中的用户
      filterLeftData()

      leftPagination.value.total = response.total || 0
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载已选用户数据
const loadSelectedUsers = async () => {
  console.log('loadSelectedUsers加载中...')
  if (!props.apiConfig.loadSelectedUsers) {
    rightTableData.value = [...props.modelValue]
    rightPagination.value.total = rightTableData.value.length
    return
  }

  try {
    const params = {
      pageSize: 999999, // 暂时使用大数值，避免分页复杂性
      ...(props.recordId && { id: props.recordId })
    }

    const response = await props.apiConfig.loadSelectedUsers(params)

    if (response && response.rows) {
      rightTableData.value = response.rows.map(item => ({
        id: item.userId,
        name: item.nickName,
        department: item.deptName,
        role: item.roleName
      }))

      rightPagination.value.total = response.total || 0

      // 更新 modelValue
      emit('update:modelValue', rightTableData.value)

      // 过滤左侧数据
      filterLeftData()
    }
  } catch (error) {
    console.error('加载已选用户数据失败:', error)
  }
}

// 加载角色选项
const loadRoleOptions = async () => {
  if (!props.apiConfig.loadRoles) {
    roleOptions.value = props.searchConfig.roleOptions || []
    return
  }

  try {
    const response = await props.apiConfig.loadRoles({pageSize: 999999})

    if (response && response.rows) {
      roleOptions.value = response.rows.map(item => ({
        label: item.roleName || '',
        value: item.roleId || ''
      }))
    }
  } catch (error) {
    console.error('加载角色选项失败:', error)
    roleOptions.value = props.searchConfig.roleOptions || []
  }
}

// 初始化数据
const initData = async () => {
  await loadRoleOptions()
  await loadSelectedUsers()
  await loadUsers()
}

// 监听 props 变化
watch(
  () => props.modelValue,
  () => {
    if (!props.apiConfig.loadSelectedUsers) {
      rightTableData.value = [...props.modelValue]
      rightPagination.value.total = rightTableData.value.length
      // 重新过滤左侧数据
      filterLeftData()
    }
  },
  { immediate: true }
)

watch(
  () => props.recordId,
  () => {
    if (props.recordId) {
      loadSelectedUsers()
    }
  }
)

// 过滤左侧数据，排除已选中的用户
const filterLeftData = () => {
  if (leftTableData.value.length === 0) return

  const selectedIds = rightTableData.value.map(item => item[props.displayConfig.keyField])
  leftTableData.value = leftTableData.value.filter(
    item => !selectedIds.includes(item[props.displayConfig.keyField])
  )
}

// 过滤后的角色选项
const filteredRoleOptions = computed(() => {
  const options = roleOptions.value.length > 0 ? roleOptions.value : props.searchConfig.roleOptions

  if (!roleSearchKeyword.value) {
    return options
  }
  return options.filter(option =>
    option.label.toLowerCase().includes(roleSearchKeyword.value.toLowerCase())
  )
})

// 处理左侧表格选择变化
const handleLeftSelectionChange = selection => {
  leftSelection.value = selection
}

// 处理右侧表格选择变化
const handleRightSelectionChange = selection => {
  rightSelection.value = selection
}

// 移动到右侧
const moveToRight = () => {
  if (props.disabled || leftSelection.value.length === 0) return

  const itemsToMove = [...leftSelection.value]

  // 添加到右侧
  rightTableData.value.push(...itemsToMove)

  // 从左侧移除
  leftTableData.value = leftTableData.value.filter(
    item =>
      !leftSelection.value.some(
        selected => selected[props.displayConfig.keyField] === item[props.displayConfig.keyField]
      )
  )

  // 清空选择
  leftSelection.value = []
  leftTableRef.value?.clearSelection()

  // 更新分页
  rightPagination.value.total = rightTableData.value.length

  // 更新左侧总数

  // 发起请求
  loadUsers()

  // 触发事件
  emit('update:modelValue', rightTableData.value)
  emit('change', rightTableData.value, 'add', itemsToMove)
}

// 移动到左侧
const moveToLeft = () => {
  if (props.disabled || rightSelection.value.length === 0) return

  const itemsToMove = [...rightSelection.value]

  // 添加到左侧
  leftTableData.value.push(...itemsToMove)

  // 从右侧移除
  rightTableData.value = rightTableData.value.filter(
    item =>
      !rightSelection.value.some(
        selected => selected[props.displayConfig.keyField] === item[props.displayConfig.keyField]
      )
  )

  // 清空选择
  rightSelection.value = []
  rightTableRef.value?.clearSelection()

  // 更新分页
  rightPagination.value.total = rightTableData.value.length

  // 发起请求
  loadUsers()

  // 触发事件
  emit('update:modelValue', rightTableData.value)
  emit('change', rightTableData.value, 'remove', itemsToMove)
}

// 处理姓名搜索
const handleNameSearch = value => {
  currentSearchParams.value.nickName = value || ''
  leftPagination.value.page = 1
  loadUsers()
  emit('name-search', value)
}

// 处理姓名搜索清空
const handleNameSearchClear = () => {
  currentSearchParams.value.nickName = ''
  leftPagination.value.page = 1
  loadUsers()
  emit('name-search', '')
}

// 处理角色选择变化
const handleRoleChange = value => {
  roleSearchKeyword.value = ''
  currentSearchParams.value.roleId = value
  leftPagination.value.page = 1
  loadUsers()
  emit('role-change', value)
}

// 处理角色选择清空
const handleRoleClear = () => {
  roleSearchKeyword.value = ''
  currentSearchParams.value.roleId = null
  leftPagination.value.page = 1
  loadUsers()
  emit('role-change', null)
}

// 处理角色远程搜索
const handleRoleRemoteSearch = query => {
  roleSearchKeyword.value = query
  emit('role-search', query)
}

// 处理左侧分页变化
const handleLeftPageChange = page => {
  leftPagination.value.page = page
  loadUsers()
  emit('left-page-change', page)
}

const handleLeftSizeChange = size => {
  leftPagination.value.pageSize = size
  leftPagination.value.page = 1
  loadUsers()
  emit('left-size-change', size)
}

// 处理右侧分页变化（暂时简化，不重新请求）
const handleRightPageChange = page => {
  rightPagination.value.page = page
}

const handleRightSizeChange = size => {
  rightPagination.value.pageSize = size
  rightPagination.value.page = 1
}

// 触发刷新
const triggerRefresh = async () => {
  if (refreshing.value) return

  refreshing.value = true
  try {
    await initData()
    emit('refresh')
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  initData()
})

// 暴露方法
defineExpose({
  triggerRefresh,
  loadUsers,
  loadSelectedUsers,
  clearSelection: () => {
    leftTableRef.value?.clearSelection()
    rightTableRef.value?.clearSelection()
    leftSelection.value = []
    rightSelection.value = []
  }
})
</script>

<style scoped>
.table-transfer-container {
  display: flex;
  gap: 20px;
  max-height: 500px;
  align-items: stretch;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 在弹窗中使用时的样式调整 */
.table-transfer-container.in-dialog {
  min-height: 400px;
  gap: 15px;
}

.transfer-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 200px;
  max-width: calc(50% - 60px);
  overflow: hidden;
  position: relative;
  contain: layout style;
}

/* 在弹窗中使用时的面板样式调整 */
.table-transfer-container.in-dialog .transfer-panel {
  min-width: 180px;
  max-width: calc(50% - 50px);
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.search-area {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
}

/* 在弹窗中使用时的搜索区域样式调整 */
.table-transfer-container.in-dialog .search-area {
  padding: 12px 16px;
}

.search-row {
  display: flex;
  gap: 12px;
}

/* 在弹窗中使用时的搜索行样式调整 */
.table-transfer-container.in-dialog .search-row {
  gap: 8px;
}

.search-input {
  flex: 1;
  min-width: 0;
}

.search-select {
  flex: 1;
  flex-shrink: 0;
}

/* 在弹窗中使用时的搜索选择框样式调整 */
.table-transfer-container.in-dialog .search-select {
  width: 120px;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0 20px 20px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.table-transfer-container.in-dialog .table-container {
  padding: 0 16px 16px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.transfer-arrows {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  flex-shrink: 0;
  min-width: 120px;
}

/* 在弹窗中使用时的操作区域样式调整 */
.table-transfer-container.in-dialog .transfer-arrows {
  padding: 15px 0;
  min-width: 100px;
}

.transfer-buttons-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.table-transfer-container.in-dialog .transfer-buttons-group {
  gap: 15px;
}

.transfer-btn {
  min-width: 120px;
  white-space: nowrap;
  margin: 0;
}

.table-transfer-container.in-dialog .transfer-btn {
  min-width: 80px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-transfer-container {
    flex-direction: column;
    gap: 16px;
  }

  .transfer-panel {
    max-width: none;
  }

  .transfer-arrows {
    min-height: auto;
    padding: 16px 0;
  }

  .transfer-buttons-group {
    flex-direction: row;
    gap: 16px;
  }

  .search-row {
    flex-direction: column;
    gap: 8px;
  }

  .search-select {
    width: 100%;
  }

  .transfer-btn {
    min-width: 100px;
    font-size: 12px;
  }
}

/* 弹窗中的响应式设计 */
@media (max-width: 1200px) {
  .table-transfer-container.in-dialog {
    gap: 10px;
  }

  .table-transfer-container.in-dialog .transfer-panel {
    min-width: 150px;
    max-width: calc(50% - 40px);
  }

  .table-transfer-container.in-dialog .transfer-arrows {
    min-width: 80px;
  }

  .table-transfer-container.in-dialog .search-row {
    flex-direction: column;
    gap: 6px;
  }

  .table-transfer-container.in-dialog .search-select {
    width: 100%;
  }
}

/* 表格样式调整 */
:deep(.el-table) {
  border: 1px solid #ebeef5;
  width: 100%;
}

:deep(.el-table th) {
  background-color: #fafafa;
}

:deep(.el-pagination) {
  --el-pagination-font-size: 12px;
  width: 100%;
  justify-content: center;
}

:deep(.el-pagination .el-select .el-input) {
  width: 100px;
}

/* 确保分页组件不会溢出 */
.transfer-panel .pagination-container {
  position: relative;
  z-index: 1;
  clear: both;
  contain: layout;
}

.transfer-panel .pagination-container .el-pagination {
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  max-width: 100%;
}

/* 左侧面板的分页 */
.left-panel .pagination-container {
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 右侧面板的分页 */
.right-panel .pagination-container {
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 在全屏模式下的特殊处理 */
.table-transfer-container {
  position: relative;
  isolation: isolate;
  container-type: inline-size;
}

.transfer-panel {
  position: relative;
  z-index: 1;
  container-type: inline-size;
}

/* 防止分页组件绝对定位导致的重叠 */
:deep(.el-pagination) {
  position: relative !important;
  transform: none !important;
  left: auto !important;
  right: auto !important;
  top: auto !important;
  bottom: auto !important;
}
</style>
