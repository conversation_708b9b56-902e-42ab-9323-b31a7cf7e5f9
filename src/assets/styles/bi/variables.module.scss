:root {
  --el-color-primary: #0B5FC5;
  /* 修改主要颜色 */
  --el-color-success: #67c23a;
  /* 修改成功颜色 */
  --el-color-warning: #e6a23c;
  /* 修改警告颜色 */
  --el-color-danger: #f56c6c;
  /* 修改危险颜色 */
  --el-color-info: #909399;
  /* 修改信息颜色 */
  --el-color-primary-light-3: #1e90db;
  --el-border-radius-base: 8px;
  --el-card-border-radius: 8px;
  --el-font-size-base: 16px;
  --el-text-color-regular: #051C2C;
  --el-border-color: #D2D2D2;
  --el-box-shadow-light: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
}

// bilayout 数据
$bi-head-height: 128px; // 头部高度
$bi-sidebar-width: 240px; // 侧边栏宽度
$bi-layout-margin: 20px; // 总体布局margin值
$bi-hide-sidebar-width: 74px; // 侧边栏收起来宽度
$bi-main-height: calc(100vh - $bi-head-height - 1px); // 总高-头部高-1px的处理顶部Marin重合问题
$bi-main-height-margin: calc(100vh - $bi-head-height - $bi-layout-margin * 2 - 1px); // 总高-头部高-marign高度-1px的处理顶部Marin重合问题

// BI默认菜单主题风格
$bi-base-background-color: #213047;
$bi-font-color: var(--el-color-primary);
$bi-sidebar-background-color: #ffffff; // 原本的$base-menu-background/menuBackground
$bi-main-background-color: #f4f4f5;
$bi-form-button: var(--el-color-primary); // 搜索按钮
 
// base color
$blue: #324157;
$light-blue: #3A71A8;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;

// 默认菜单主题风格
$base-menu-color: #bfcbd9;
$base-menu-color-active: #f4f4f5;
$base-logo-title-color: #ffffff;

$base-menu-light-color: rgba(0, 0, 0, 0.7);
$base-menu-light-background: #ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background: #1f2d3d;
$base-sub-menu-hover: #001528;

// 自定义暗色菜单风格
/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/

$--color-primary: #409EFF;
$--color-success: #67C23A;
$--color-warning: #E6A23C;
$--color-danger: #F56C6C;
$--color-info: #909399;
 

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuColor: $base-menu-color;
  menuLightColor: $base-menu-light-color;
  menuColorActive: $base-menu-color-active;
  menuLightBackground: $base-menu-light-background;
  subMenuBackground: $base-sub-menu-background;
  subMenuHover: $base-sub-menu-hover;
  logoTitleColor: $base-logo-title-color;
  logoLightTitleColor: $base-logo-light-title-color;
  primaryColor: $--color-primary;
  successColor: $--color-success;
  dangerColor: $--color-danger;
  infoColor: $--color-info;
  warningColor: $--color-warning;

  biHeadHeight: $bi-head-height;
  biSidebarWidth: $bi-sidebar-width;
  biLayoutMargin: $bi-layout-margin;
  biHideSidebarWidth: $bi-hide-sidebar-width;

  biBaseBackgroundColor: $bi-base-background-color;
  biMainBackgroundColor: $bi-main-background-color;
  biSidebarBackgroundColor: $bi-sidebar-background-color;

  // $bi-main-height
  biMainHeight: calc(100vh - #{$bi-head-height});

}